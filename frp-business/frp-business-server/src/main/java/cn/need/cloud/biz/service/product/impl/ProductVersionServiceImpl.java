package cn.need.cloud.biz.service.product.impl;

import cn.hutool.core.collection.CollUtil;
import cn.need.cloud.biz.cache.ProductVersionCacheRepertory;
import cn.need.cloud.biz.cache.bean.ProductCache;
import cn.need.cloud.biz.cache.bean.ProductVersionCache;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.inbound.InboundMeasureTypeEnum;
import cn.need.cloud.biz.client.constant.enums.inbound.InboundWorkOrderStatusEnum;
import cn.need.cloud.biz.client.constant.enums.product.ProductLogStatusEnum;
import cn.need.cloud.biz.converter.product.ProductVersionConverter;
import cn.need.cloud.biz.mapper.product.ProductMapper;
import cn.need.cloud.biz.mapper.product.ProductVersionMapper;
import cn.need.cloud.biz.model.bo.product.ProductRemeasureBO;
import cn.need.cloud.biz.model.entity.inbound.InboundWorkorder;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.model.entity.product.Product;
import cn.need.cloud.biz.model.entity.product.ProductVersion;
import cn.need.cloud.biz.model.param.product.create.ProductCreateParam;
import cn.need.cloud.biz.model.param.product.create.ProductVersionCreateParam;
import cn.need.cloud.biz.model.param.product.update.ProductVersionUpdateParam;
import cn.need.cloud.biz.model.query.log.AuditShowLogQuery;
import cn.need.cloud.biz.model.query.otb.pickingslip.OtbPickingSlipMarkRemeasureCartonSizeQuery;
import cn.need.cloud.biz.model.query.product.ProductVersionQuery;
import cn.need.cloud.biz.model.vo.base.BasePartnerVO;
import cn.need.cloud.biz.model.vo.inbound.workorder.InboundMeasureVO;
import cn.need.cloud.biz.model.vo.log.AuditShowLogPageVO;
import cn.need.cloud.biz.model.vo.product.ProductMeasureVO;
import cn.need.cloud.biz.model.vo.product.ProductVersionVO;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailService;
import cn.need.cloud.biz.service.helper.auditshowlog.inbound.InboundWorkOrderAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.product.ProductAuditLogHelper;
import cn.need.cloud.biz.service.inbound.InboundRequestDetailService;
import cn.need.cloud.biz.service.inbound.InboundWorkorderService;
import cn.need.cloud.biz.service.log.AuditShowLogService;
import cn.need.cloud.biz.service.product.ProductVersionService;
import cn.need.cloud.biz.util.JsonUtil;
import cn.need.cloud.biz.util.ModifyCompareUtil;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.cloud.biz.util.log.AuditLogUtil;
import cn.need.cloud.upms.cache.TenantCacheService;
import cn.need.cloud.upms.cache.bean.TenantCache;
import cn.need.cloud.upms.cache.util.TenantCacheUtil;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 产品版本详情 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class ProductVersionServiceImpl extends SuperServiceImpl<ProductVersionMapper, ProductVersion> implements ProductVersionService {


    @Resource
    private ProductMapper productMapper;

    @Resource
    private InboundRequestDetailService inboundRequestDetailService;

    @Resource
    private BinLocationDetailService binLocationDetailService;

    @Resource
    private TenantCacheService tenantCacheService;

    @Resource
    private ProductVersionCacheRepertory productVersionCacheRepertory;

    @Resource
    private AuditShowLogService auditShowLogService;

    @Resource
    @Lazy
    private InboundWorkorderService inboundWorkorderService;

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(ProductVersionCreateParam createParam) {
        // 检查传入产品版本详情参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        //校验
        Long productId = createParam.getProductId();
        //校验versionInt唯一
        checkVersionInt(createParam.getProductVersionInt(), productId);
        // 获取产品版本详情转换器实例，用于将产品版本详情参数对象转换为实体对象
        ProductVersionConverter converter = Converters.get(ProductVersionConverter.class);
        // 将产品版本详情参数对象转换为实体对象并初始化
        ProductVersion entity = initProductVersion(converter.toEntity(createParam));
        fillProductVersionEntity(entity);
        //获取产品版本
        ProductCache cache = ProductCacheUtil.getById(productId);
        // 记录到log日志
        ProductAuditLogHelper.recordLog(
                BeanUtil.copyNew(cache, Product.class),
                ProductLogStatusEnum.ADD.getStatus(),
                BaseTypeLogEnum.VERSIONS.getType(),
                null,
                entity.toLog()
        );
        // 插入产品版本详情实体对象到数据库
        super.insert(entity);
        // 返回产品版本详情ID
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(ProductVersionUpdateParam updateParam) {
        // 检查传入产品版本详情参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        //校验
        Integer productVersionInt = updateParam.getProductVersionInt();
        ProductVersion oldEntity = super.getById(updateParam.getId());
        if (ObjectUtil.notEqual(productVersionInt, oldEntity.getProductVersionInt())) {
            checkVersionInt(productVersionInt, updateParam.getProductId());
        }
        // 获取产品版本详情转换器实例，用于将产品版本详情参数对象转换为实体对象
        ProductVersionConverter converter = Converters.get(ProductVersionConverter.class);
        // 将产品版本详情参数对象转换为实体对象
        ProductVersion entity = converter.toEntity(updateParam);
        // 执行更新产品版本详情操作
        int update = super.update(entity);

        // 记录到log日志
        ProductVersion newEntity = super.getById(updateParam.getId());
        //获取产品
        ProductCache cache = ProductCacheUtil.getById(newEntity.getProductId());
        ProductAuditLogHelper.recordLog(
                BeanUtil.copyNew(cache, Product.class),
                ProductLogStatusEnum.CHANGE.getStatus(),
                BaseTypeLogEnum.VERSIONS.getType(),
                null,
                ModifyCompareUtil.recordModifyLog(newEntity, oldEntity)
        );
        //Dimension Change
        ProductAuditLogHelper.recordLog(
                BeanUtil.copyNew(cache, Product.class),
                ProductLogStatusEnum.CHANGE.getStatus(),
                BaseTypeLogEnum.VERSIONS.getType(),
                null,
                AuditLogUtil.PRODUCT_VERSION_PREFIX + newEntity.getProductVersionInt() + ModifyCompareUtil.recordModifyLog(newEntity, oldEntity)
        );
        //清缓存
        productVersionCacheRepertory.delProductVersion(updateParam.getId().toString());

        return update;

    }

    @Override
    public List<ProductVersionVO> listByQuery(ProductVersionQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<ProductVersionVO> pageByQuery(PageSearch<ProductVersionQuery> search) {
        Page<ProductVersion> page = Conditions.page(search, entityClass);
        List<ProductVersionVO> dataList = mapper.listByQuery(search.getCondition(), page);
        //判空
        if (CollUtil.isEmpty(dataList)) {
            return new PageData<>(dataList, page);
        }
        //获取供应商id
        Set<Long> tenantIdList = dataList.stream()
                .map(ProductVersionVO::getTransactionPartnerId)
                .collect(Collectors.toSet());
        //根据供应商id映射供应商信息
        Map<Long, TenantCache> tenantCacheMap = ObjectUtil.toMap(TenantCacheUtil.listByIds(tenantIdList), TenantCache::getId);
        //获取产品id
        Set<Long> productIdList = dataList
                .stream()
                .map(ProductVersionVO::getProductId)
                .collect(Collectors.toSet());
        //根据产品id映射产品信息
        Map<Long, ProductCache> productCacheMap = ObjectUtil.toMap(ProductCacheUtil.listByIds(productIdList), ProductCache::getId);
        //遍历dataList，填充供应商信息
        dataList.forEach(productVO -> {
            //填充供应商信息
            TenantCache tenantCache = tenantCacheMap.get(productVO.getTransactionPartnerId());
            Optional.ofNullable(tenantCache).ifPresent(item -> {
                productVO.setTransactionPartner(BeanUtil.copyNew(item, BasePartnerVO.class));
            });
            ProductCache productCache = productCacheMap.get(productVO.getProductId());
            Optional.ofNullable(productCache).ifPresent(item -> {
                productVO.setProductType(item.getProductType());
            });
        });
        return new PageData<>(dataList, page);
    }

    @Override
    public ProductVersionVO detailById(Long id) {
        ProductVersion entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in ProductVersion");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "ProductVersion", id));
        }

        ProductVersionVO productVersionVO = buildProductVersionVO(entity);
        if (ObjectUtil.isEmpty(productVersionVO)) {
            throw new BusinessException("productVersionVO is empty");
        }
        // 获取供应商信息
        if (ObjectUtil.isNotEmpty(tenantCacheService.getById(productVersionVO.getTransactionPartnerId()))) {
            productVersionVO.setTransactionPartner(
                    BeanUtil.copyNew(tenantCacheService.getById(productVersionVO.getTransactionPartnerId())
                            , BasePartnerVO.class));
        }
        return productVersionVO;
    }

    @Override
    public ProductVersionVO detailByRefNum(String refNum) {
        ProductVersion entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("RefNum: " + refNum + " not found in ProductVersion");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "ProductVersion", "refNum", refNum));
        }
        return buildProductVersionVO(entity);
    }

    @Override
    public void initProductVersionByProductCreateParam(ProductCreateParam createParam, Product entity) {
        // 获取产品版本转换器实例，用于将产品参数对象转换为实体对象
        ProductVersionConverter productVersionConverter = Converters.get(ProductVersionConverter.class);
        // 将参数对象转换为产品版本实体对象并初始化
        ProductVersion productVersion = productVersionConverter.toEntity(createParam);
        productVersion.setProductId(entity.getId());
        productVersion.setRefNum(entity.getRefNum());
        productVersion.setProductVersionInt(NumberUtils.INTEGER_ZERO);
        productVersion.setProductRemeasureType(InboundMeasureTypeEnum.NONE.getStatus());
        // 插入产品版本实体对象到数据库
        super.insert(productVersion);
    }


    @Override
    public boolean existByProductId(Long productId) {
        return lambdaQuery().eq(ProductVersion::getProductId, productId).count() > 0;
    }

    @Override
    public Set<Long> getProductVersionIds(Set<String> productRefNumList, Set<String> upcList, Set<String> supplierSkuList) {
        //参数是否全为空
        boolean flag = false;
        //创建条件选择器
        LambdaQueryWrapper<ProductVersion> queryWrapper = new LambdaQueryWrapper<>();
        //判空
        if (emptyFlag(queryWrapper, productRefNumList, upcList, supplierSkuList)) {
            return CollUtil.newHashSet();
        }
        //获取产品集合
        List<ProductVersion> list = list(queryWrapper);
        //判空
        if (ObjectUtil.isEmpty(list)) {
            return CollUtil.newHashSet(-1L);
        }
        //添加缓存
        productVersionCacheRepertory.addProductVersion(BeanUtil.copyNew(list, ProductVersionCache.class));
        //返回版本产品id
        return list.stream().map(ProductVersion::getId).collect(Collectors.toSet());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTitle(Long productId, String title) {
        List<ProductVersion> productVersionList = findByProductId(productId);
        productVersionList.forEach(productVersion -> productVersion.setTitle(title));
        super.updateBatch(productVersionList);
        //清缓存
        List<String> versionIdList = productVersionList.stream().map(productVersion -> productVersion.getId().toString()).toList();
        productVersionCacheRepertory.delProductVersion(versionIdList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSupplierSkuAndUpc(Long productId, String supplierSku, String upc) {
        // 获取产品的所有版本
        List<ProductVersion> productVersionList = findByProductId(productId);

        if (ObjectUtil.isEmpty(productVersionList)) {
            return;
        }

        // 更新每个版本的供应商SKU和UPC
        boolean hasChanges = false;
        for (ProductVersion productVersion : productVersionList) {
            if (ObjectUtil.isNotEmpty(supplierSku) && !StringUtil.equals(productVersion.getSupplierSku(), supplierSku)) {
                productVersion.setSupplierSku(supplierSku);
                hasChanges = true;
            }
            if (ObjectUtil.isNotEmpty(upc) && !StringUtil.equals(productVersion.getUpc(), upc)) {
                productVersion.setUpc(upc);
                hasChanges = true;
            }
        }

        // 如果有变更，则批量更新并清理缓存
        if (hasChanges) {
            super.updateBatch(productVersionList);
            //清缓存
            List<String> versionIdList = productVersionList.stream()
                    .map(productVersion -> productVersion.getId().toString())
                    .toList();
            productVersionCacheRepertory.delProductVersion(versionIdList);
        }
    }

    @Override
    public List<ProductVersion> getProductVersionIds(Set<Long> productIdList) {
        if (ObjectUtil.isEmpty(productIdList)) {
            return Lists.arrayList();
        }
        return lambdaQuery()
                .in(ProductVersion::getProductId, productIdList)
                .select(ProductVersion::getId, ProductVersion::getProductId)
                .list();
    }

    @Override
    public List<ProductVersionVO> getDimensionChangeProduct(LocalDateTime startLastModificationTime, LocalDateTime endLastModificationTime) {

        AuditShowLogQuery auditShowLogQuery = new AuditShowLogQuery();
        auditShowLogQuery.setCreateTimeStart(startLastModificationTime);
        auditShowLogQuery.setCreateTimeEnd(endLastModificationTime);
        auditShowLogQuery.setTypeList(Arrays.asList(ProductLogStatusEnum.DIMENSION_CHANGE.getStatus(), ProductLogStatusEnum.REMEASURE.getStatus()));
        List<AuditShowLogPageVO> auditShowLogPageVOS = auditShowLogService.listByQuery(auditShowLogQuery);
        if (CollUtil.isEmpty(auditShowLogPageVOS)) {
            return Collections.emptyList();
        }

        List<String> productRefNums = auditShowLogPageVOS.stream().map(AuditShowLogPageVO::getRefTableRefNum).distinct().toList();
        return lambdaQuery().in(ProductVersion::getRefNum, productRefNums).list().stream()
                .map(this::buildProductVersionVO)
                .collect(Collectors.toList());
    }

    @Override
    public void updateMeasureStatus(InboundMeasureVO measureVO) {
        //获取产品版本
        ProductVersion version = super.getById(measureVO.getProductVersionId());
        ProductVersion productVersion = BeanUtil.copyNew(measureVO, ProductVersion.class);
        productVersion.setId(measureVO.getProductVersionId());
        productVersion.setRefNum(version.getRefNum());
        productVersion.setVersion(version.getVersion());

        if (InboundMeasureTypeEnum.NONE.getStatus().equals(version.getProductRemeasureType())) {
            //记录 First Inbound Dimension Check
            saveLogOnlyForProduct(measureVO, BeanUtil.copyNew(version, ProductVersionCache.class), "First Inbound Dimension Check");
        }
        productVersion.setProductRemeasureType(InboundMeasureTypeEnum.Remeasured.getStatus());
        super.update(productVersion);
        //删除缓存
        productVersionCacheRepertory.delProductVersion(StringUtil.toString(productVersion.getId()));
        //记录日志
        saveLog(measureVO, BeanUtil.copyNew(version, ProductVersionCache.class));
    }

    @Override
    public ProductVersion findByProductIdAndVersionId(Long productId, Integer versionInt) {
        return lambdaQuery()
                .eq(ProductVersion::getProductId, productId)
                .eq(ProductVersion::getProductVersionInt, versionInt)
                .last("limit 1")
                .one();
    }

    @Override
    public List<ProductVersion> findByProductId(Long productId) {
        return lambdaQuery()
                .eq(ProductVersion::getProductId, productId)
                .list();
    }

    @Override
    public ProductVersion findLatestProductVersionByProductId(Long productId) {
        return lambdaQuery()
                .eq(ProductVersion::getProductId, productId)
                .orderByDesc(ProductVersion::getProductVersionInt)
                .last("limit 1")
                .one();
    }

    /**
     * 根据产品ID获取最新产品版本信息
     * <p>
     * 此方法旨在通过产品的ID来查找该产品的最新版本信息它首先检查传入的产品ID是否为null，
     * 如果不为null，则调用另一个方法来获取最新版本信息这个方法利用了已有的按产品ID列表获取最新版本信息的功能，
     * 通过传递一个只包含单个产品ID的列表来实现
     *
     * @param productId 产品的ID，用于查找最新版本信息如果ID为null，方法将返回null
     * @return 返回一个ProductVersionVO对象，表示最新产品版本信息如果找不到或输入ID为null，则返回null
     */
    @Override
    public ProductVersionVO getLatestProductVersionByProductId(Long productId) {
        // 检查产品ID是否为null，如果为null则直接返回null
        if (productId == null) {
            return null;
        }
        // 调用按产品ID列表获取最新版本信息的方法，并通过产品ID获取对应的最新版本信息
        return getLatestProductVersionByProductIdList(Collections.singletonList(productId)).get(productId);
    }


    /**
     * 根据产品ID列表获取每个产品的最新版本信息
     * 此方法首先检查输入的 productIdList 是否为空，如果为空，则返回一个空的Map
     * 如果列表不为空，它将调用 mapper 方法来获取对应产品ID的最新版本信息列表
     * 最后，它将这个列表转换成一个Map，其中键是产品ID，值是对应的产品版本信息
     * 这种方法便于快速查找和访问每个产品的最新版本信息
     *
     * @param productIdList 产品ID列表，用于查询每个产品的最新版本信息
     * @return 返回一个Map，键是产品ID，值是对应的产品版本信息（ProductVersionVO）
     */
    @Override
    public Map<Long, ProductVersionVO> getLatestProductVersionByProductIdList(List<Long> productIdList) {
        //判空：避免空指针或无效查询
        if (ObjectUtil.isEmpty(productIdList)) {
            return Collections.emptyMap();
        }
        productIdList = new HashSet<>(productIdList).stream().toList();
        //根据产品ID列表查询每个产品的最新版本信息
        final List<ProductVersionVO> productVersionVOList = mapper.gretLatestProductVersionByProductIdList(productIdList);

        //将查询结果列表转换为Map，便于通过产品ID快速访问最新版本信息
        return ObjectUtil.toMap(productVersionVOList, ProductVersionVO::getProductId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProductVersion remeasureCartonSize(OtbPickingSlipMarkRemeasureCartonSizeQuery query) {
        ProductVersion productVersion = this.getById(query.getProductVersionId());
        ProductRemeasureBO before = BeanUtil.copyNew(productVersion, ProductRemeasureBO.class);
        // 变更部分
        ProductRemeasureBO change = BeanUtil.copyNew(query, ProductRemeasureBO.class);
        change.setId(query.getProductVersionId());
        // 字符比较
        String changeStr = JsonUtil.toJson(change);
        String beforeStr = JsonUtil.toJson(before);
        // 更新逻辑
        boolean isDiff = !changeStr.equals(beforeStr);
        if (isDiff) {
            // 更新
            Validate.isTrue(this.update(BeanUtil.copyNew(change, ProductVersion.class)) == 1,
                    "{} RemeasureCartonSize update fail", productVersion.getSupplierSku()
            );
        }

        Product product = this.productMapper.selectById(productVersion.getProductId());
        AuditShowLog changeLog = AuditLogUtil.commonLog(product)
                // 发生变更
                .with(isDiff, AuditShowLog::setEvent, "RemeasureCartonSize Change")
                .with(isDiff, AuditShowLog::setDescription, String.format(
                        "ProductVersionInt: %s , %s",
                        productVersion.getProductVersionInt(),
                        changeStr
                ))
                // 未变更
                .with(!isDiff, AuditShowLog::setEvent, "RemeasureCartonSize No Change")
                .with(!isDiff, AuditShowLog::setDescription, String.format("ProductVersionInt: %s ",
                        productVersion.getProductVersionInt()
                ))
                .build();
        // 记录日志
        AuditLogHolder.record(changeLog);

        return productVersion;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(ProductVersion entity) {
        int update = super.update(entity);
        // 删除缓存
        productVersionCacheRepertory.delProductVersion(StringUtil.toString(entity.getId()));
        return update;
    }

    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * 记录规格更改日志
     *
     * @param measureVO 规格
     * @param version   产品版本
     */
    private void saveLogOnlyForProduct(
            InboundMeasureVO measureVO,
            ProductVersionCache version,
            String auditLogEvent) {
        //测量class对象
        ProductMeasureVO newProductMeasureVO = BeanUtil.copyNew(measureVO, ProductMeasureVO.class);
        ProductMeasureVO oldProductMeasureVO = BeanUtil.copyNew(version, ProductMeasureVO.class);
        //获取产品信息
        Product product = productMapper.selectById(version.getProductId());
        ProductAuditLogHelper.recordLog(
                product,
                auditLogEvent,
                BaseTypeLogEnum.OPERATION.getType(),
                null,
                AuditLogUtil.PRODUCT_VERSION_PREFIX + version.getProductVersionInt() + ModifyCompareUtil.recordModifyLog(newProductMeasureVO, oldProductMeasureVO)
        );
    }

    /**
     * 记录规格更改日志
     *
     * @param measureVO 规格
     * @param version   产品版本
     */
    private void saveLog(InboundMeasureVO measureVO, ProductVersionCache version) {
        //测量class对象
        ProductMeasureVO newProductMeasureVO = BeanUtil.copyNew(measureVO, ProductMeasureVO.class);
        ProductMeasureVO oldProductMeasureVO = BeanUtil.copyNew(version, ProductMeasureVO.class);
        // 检查每箱数量是否为空，以决定后续操作的类型
        if (ObjectUtil.isEmpty(measureVO.getPcsPerCarton())) {
            // 如果每箱数量为空，则调用无需更改产品的日志保存方法
            saveLogWithoutChangeProduct(measureVO, version, oldProductMeasureVO);
            return;
        }
        // 如果每箱数量不为空，则调用包含更改的日志保存方法
        saveLogWithChange(measureVO, version, newProductMeasureVO, oldProductMeasureVO);
    }

    /**
     * 记录规格重测后无变化的日志
     *
     * @param measureVO           规格
     * @param version             产品版本
     * @param oldProductMeasureVO 旧规格
     */
    private void saveLogWithoutChangeProduct(InboundMeasureVO measureVO, ProductVersionCache version, ProductMeasureVO oldProductMeasureVO) {
        //获取产品信息
        Product product = productMapper.selectById(version.getProductId());
        ProductAuditLogHelper.recordLog(
                product,
                AuditLogUtil.PRODUCT_VERSION_PREFIX + version.getProductVersionInt(),
                ProductLogStatusEnum.REMEASURE_NO_CHANGE.getStatus()
        );
        //获取工单信息
        InboundWorkorder inboundWorkorder = inboundWorkorderService.getById(measureVO.getWorkOrderId());
        oldProductMeasureVO.setProduct(version.toLog());
        InboundWorkOrderAuditLogHelper.recordWithStatus(
                inboundWorkorder,
                InboundWorkOrderStatusEnum.MEASURE.getStatus(),
                BaseTypeLogEnum.OPERATION.getType(),
                JsonUtil.toJson(oldProductMeasureVO)
        );
    }

    /**
     * 记录规格重测后变化的日志
     *
     * @param measureVO           规格
     * @param version             产品版本
     * @param newProductMeasureVO 新规格
     * @param oldProductMeasureVO 旧规格
     */
    private void saveLogWithChange(
            InboundMeasureVO measureVO,
            ProductVersionCache version,
            ProductMeasureVO newProductMeasureVO,
            ProductMeasureVO oldProductMeasureVO) {
        //获取产品信息
        Product product = productMapper.selectById(version.getProductId());
        ProductAuditLogHelper.recordLog(
                product,
                ProductLogStatusEnum.REMEASURE.getStatus(),
                BaseTypeLogEnum.OPERATION.getType(),
                null,
                AuditLogUtil.PRODUCT_VERSION_PREFIX + version.getProductVersionInt() + ModifyCompareUtil.recordModifyLog(newProductMeasureVO, oldProductMeasureVO)
        );
        //获取工单信息
        InboundWorkorder inboundWorkorder = inboundWorkorderService.getById(measureVO.getWorkOrderId());
        oldProductMeasureVO.setProduct(version.toLog());
        InboundWorkOrderAuditLogHelper.recordWithStatus(
                inboundWorkorder,
                InboundWorkOrderStatusEnum.MEASURE.getStatus(),
                BaseTypeLogEnum.OPERATION.getType(),
                JsonUtil.toJson(oldProductMeasureVO));
    }


    /**
     * 判空
     * 该方法用于给查询条件判空，传入条件选择器
     * 1 若查询参数都为空则返回true  反之 false
     *
     * @param queryWrapper      版本产品条件选择器
     * @param upcList           版本产品条件选择器
     * @param supplierSkuList   upcList
     * @param productRefNumList 版本产品条件选择器
     * @return 布尔类型
     */
    private boolean emptyFlag(
            LambdaQueryWrapper<ProductVersion> queryWrapper,
            Set<String> productRefNumList,
            Set<String> upcList, Set<String> supplierSkuList) {
        boolean flag = true;
        //产品版本号
        if (ObjectUtil.isNotEmpty(productRefNumList)) {
            queryWrapper.in(ProductVersion::getRefNum, productRefNumList);
            flag = false;
        }
        //产品upc
        if (ObjectUtil.isNotEmpty(upcList)) {
            queryWrapper.in(ProductVersion::getUpc, upcList);
            flag = false;
        }
        //供应商sku
        if (ObjectUtil.isNotEmpty(supplierSkuList)) {
            queryWrapper.in(ProductVersion::getSupplierSku, supplierSkuList);
            flag = false;
        }
        return flag;
    }

    /**
     * 构建产品版本详情VO对象
     *
     * @param entity 产品版本详情对象
     * @return 返回包含详细信息的产品版本详情VO对象
     */
    private ProductVersionVO buildProductVersionVO(ProductVersion entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的产品版本详情VO对象
        return Converters.get(ProductVersionConverter.class).toVO(entity);
    }

    /**
     * 初始化产品版本详情对象
     * 此方法用于设置产品版本详情对象的必要参数，确保其处于有效状态
     *
     * @param entity 产品版本详情对象，不应为空
     * @return 返回初始化后的产品版本详情
     * @throws BusinessException 如果传入的产品版本详情为空，则抛出此异常
     */
    private ProductVersion initProductVersion(ProductVersion entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("ProductVersion cannot be empty");
        }

        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 检验 ProductVersionInt 是否已经存在
     *
     * @param productVersionInt 产品版本号
     * @param productId         产品id
     */
    private void checkVersionInt(Integer productVersionInt, Long productId) {
        if (ObjectUtil.isEmpty(productVersionInt) || productVersionInt <= 0 || ObjectUtil.isEmpty(productId)) {
            throw new BusinessException("ProductVersionInt or productId is not valid");
        }
        //检验 ProductVersionInt唯一
        if (existProductVersionIntByProductId(productVersionInt, productId)) {
            throw new BusinessException("ProductVersionInt: " + productVersionInt + " already exists in Product");
        }
    }

    /**
     * 填充产品版本详情实体对象
     *
     * @param entity 产品版本详情实体对象
     */
    private void fillProductVersionEntity(ProductVersion entity) {
        Long productId = entity.getProductId();
        Product product = productMapper.selectById(productId);
        if (ObjectUtil.isEmpty(product)) {
            throw new BusinessException("productId: " + productId + " not found in Product");
        }
        // 填充相关字段
        entity.setId(IdWorker.getId());
        entity.setUpc(product.getUpc());
        entity.setTitle(product.getTitle());
        entity.setRefNum(product.getRefNum());
        entity.setSupplierSku(product.getSupplierSku());
        entity.setTransactionPartnerId(product.getTransactionPartnerId());
        entity.setProductRemeasureType(InboundMeasureTypeEnum.NONE.getStatus());
    }

    /**
     * 检验 ProductVersionInt是否已经存在
     *
     * @param productVersionInt 产品版本号
     * @param productId         产品id
     * @return boolean
     */
    private boolean existProductVersionIntByProductId(Integer productVersionInt, Long productId) {
        return lambdaQuery().eq(ProductVersion::getProductVersionInt, productVersionInt)
                .eq(ProductVersion::getProductId, productId).count() > 0;
    }

}
