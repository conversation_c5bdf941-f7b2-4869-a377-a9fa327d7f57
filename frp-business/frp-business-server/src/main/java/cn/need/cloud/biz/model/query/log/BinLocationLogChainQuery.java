package cn.need.cloud.biz.model.query.log;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * BinLocationLog 链路查询参数
 *
 * <AUTHOR> Assistant
 * @since 2024-12-08
 */
@Data
@Schema(description = "BinLocationLog 链路查询参数")
public class BinLocationLogChainQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 最早时间（必填）- 查询此时间点及之后的数据
     */
    @NotNull(message = "最早时间不能为空")
    @Schema(description = "最早时间 - 查询此时间点及之后的数据", required = true)
    private LocalDateTime earliestTime;

    /**
     * 产品ID（必填）
     */
    @NotNull(message = "产品ID不能为空")
    @Schema(description = "产品ID", required = true)
    private Long productId;

    /**
     * 当前库存数量（可选，用于计算 currentTotalInStockQty）
     */
    @Schema(description = "当前库存数量（可选，用于计算 currentTotalInStockQty）")
    private Integer currentInStockQty;
}
