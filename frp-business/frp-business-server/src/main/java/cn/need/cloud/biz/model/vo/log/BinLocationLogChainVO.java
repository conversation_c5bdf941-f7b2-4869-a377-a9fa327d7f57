package cn.need.cloud.biz.model.vo.log;

import cn.need.cloud.biz.model.vo.base.BaseProductVO;
import cn.need.cloud.biz.model.vo.base.BaseWarehouseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * BinLocationLog 链路查询结果 VO
 *
 * <AUTHOR> Assistant
 * @since 2024-12-08
 */
@Data
@Schema(description = "BinLocationLog 链路查询结果 VO")
public class BinLocationLogChainVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 产品版本ID
     */
    @Schema(description = "产品版本ID")
    private Long productVersionId;

    /**
     * 库位详情ID
     */
    @Schema(description = "库位详情ID")
    private Long binLocationDetailId;

    /**
     * 日志ID
     */
    @Schema(description = "日志ID")
    private Long logId;

    /**
     * 数量侧（source/dest）
     */
    @Schema(description = "数量侧（source/dest）")
    private String qtySide;

    /**
     * 变更前数量
     */
    @Schema(description = "变更前数量")
    private Integer beforeQty;

    /**
     * 变更数量
     */
    @Schema(description = "变更数量")
    private Integer changeQty;

    /**
     * 变更后数量
     */
    @Schema(description = "变更后数量")
    private Integer afterQty;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 上一条记录的变更后数量
     */
    @Schema(description = "上一条记录的变更后数量")
    private Integer prevAfterQty;

    /**
     * 链路是否正常（1-正常，0-断档，null-首条记录）
     */
    @Schema(description = "链路是否正常（1-正常，0-断档，null-首条记录）")
    private Integer isChainOk;

    /**
     * 变更类型
     */
    @Schema(description = "变更类型")
    private String changeType;

    /**
     * 产品编号（从缓存获取）
     */
    @Schema(description = "产品编号")
    private String productRefNum;

    /**
     * 供应商SKU（从缓存获取）
     */
    @Schema(description = "供应商SKU")
    private String supplierSku;

    /**
     * 库位名称（从缓存获取）
     */
    @Schema(description = "库位名称")
    private String locationName;

    /**
     * 当前总库存数量（计算值）
     */
    @Schema(description = "当前总库存数量（计算值）")
    private Integer currentTotalInStockQty;

    /**
     * 库位ID（用于查询缓存）
     */
    @Schema(description = "库位ID")
    private Long binLocationId;

    /**
     * 产品ID
     */
    @Schema(description = "产品ID")
    private Long productId;

    /**
     * 库位ID（bin_location表的ID）
     */
    @Schema(description = "库位ID（bin_location表的ID）")
    private Long binlocationId;

    // ========== BinLocationLog 表的所有字段 ==========

    /**
     * 版本号
     */
    @Schema(description = "版本号")
    private Long version;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private Long createBy;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private Long updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @Schema(description = "删除标记")
    private Integer removeFlag;

    /**
     * 关联表ID
     */
    @Schema(description = "关联表ID")
    private Long refTableId;

    /**
     * 关联表名称
     */
    @Schema(description = "关联表名称")
    private String refTableName;

    /**
     * 关联表编号
     */
    @Schema(description = "关联表编号")
    private String refTableRefNum;

    /**
     * 关联表显示名称
     */
    @Schema(description = "关联表显示名称")
    private String refTableShowName;

    /**
     * 关联表显示编号
     */
    @Schema(description = "关联表显示编号")
    private String refTableShowRefNum;

    /**
     * 源库位ID
     */
    @Schema(description = "源库位ID")
    private Long sourceBinLocationId;

    /**
     * 源库位详情ID
     */
    @Schema(description = "源库位详情ID")
    private Long sourceBinLocationDetailId;

    /**
     * 源库位变更数量
     */
    @Schema(description = "源库位变更数量")
    private Integer sourceChangeInStockQty;

    /**
     * 源库位变更前数量
     */
    @Schema(description = "源库位变更前数量")
    private Integer sourceBeforeInStockQty;

    /**
     * 源库位变更后数量
     */
    @Schema(description = "源库位变更后数量")
    private Integer sourceAfterInStockQty;

    /**
     * 目标库位ID
     */
    @Schema(description = "目标库位ID")
    private Long destBinLocationId;

    /**
     * 目标库位详情ID
     */
    @Schema(description = "目标库位详情ID")
    private Long destBinLocationDetailId;

    /**
     * 目标库位变更数量
     */
    @Schema(description = "目标库位变更数量")
    private Integer destChangeInStockQty;

    /**
     * 目标库位变更前数量
     */
    @Schema(description = "目标库位变更前数量")
    private Integer destBeforeInStockQty;

    /**
     * 目标库位变更后数量
     */
    @Schema(description = "目标库位变更后数量")
    private Integer destAfterInStockQty;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 删除备注
     */
    @Schema(description = "删除备注")
    private String deletedNote;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;

    /**
     * 仓库ID
     */
    @Schema(description = "仓库ID")
    private Long warehouseId;
}
