# BinLocationLog 链路查询功能测试指南

## 修复完成总结

✅ **已修复的问题**:
1. 在 CTE 的 `qty_union` 中添加了 `tenant_id` 和 `warehouse_id` 字段
2. 确保所有后续查询都能正确访问这些字段
3. 字段名从 `createTime` 修改为 `earliestTime`
4. SQL 查询条件从 `<=` 修改为 `>=`

## 测试步骤

### 1. 编译测试
```bash
# 在 frp-business 目录下执行
mvn clean compile
```

### 2. 单元测试（可选）
创建一个简单的单元测试来验证 Mapper 方法：

```java
@Test
public void testListChainByQuery() {
    BinLocationLogChainQuery query = new BinLocationLogChainQuery();
    query.setEarliestTime(LocalDateTime.of(2024, 12, 1, 0, 0, 0));
    query.setProductId(12345L);
    query.setCurrentInStockQty(100);
    
    List<BinLocationLogChainVO> result = binLocationLogMapper.listChainByQuery(query);
    
    assertNotNull(result);
    // 验证结果
}
```

### 3. API 测试
使用 Postman 或 curl 测试 API 接口：

```bash
curl -X POST "http://localhost:8080/api/biz/inventory/bin-location-log-chain" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Warehouse-Id: YOUR_WAREHOUSE_ID" \
  -d '{
    "earliestTime": "2024-12-01T00:00:00",
    "productId": 12345,
    "currentInStockQty": 100
  }'
```

### 4. 验证点

#### 4.1 SQL 执行验证
- 确认 SQL 能正确执行，没有字段不存在的错误
- 确认 `tenant_id` 和 `warehouse_id` 字段被正确处理

#### 4.2 数据正确性验证
- 验证查询结果按时间倒序排列
- 验证链路校验逻辑（`isChainOk` 字段）
- 验证 `currentTotalInStockQty` 计算正确

#### 4.3 缓存数据验证
- 验证产品信息（`productRefNum`, `supplierSku`）正确填充
- 验证库位信息（`locationName`）正确填充

## 预期结果

### 成功响应示例
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "productVersionId": 12345,
      "binLocationDetailId": 67890,
      "logId": 111,
      "qtySide": "source",
      "beforeQty": 100,
      "changeQty": -10,
      "afterQty": 90,
      "createTime": "2024-12-08T10:30:00",
      "prevAfterQty": 100,
      "isChainOk": 1,
      "changeType": "pick",
      "productRefNum": "SKU-XIONGGU-00004",
      "supplierSku": "SUPPLIER-SKU-001",
      "locationName": "A-01-01",
      "currentTotalInStockQty": 100,
      "binLocationId": 123,
      "productId": 12345,
      "binlocationId": 456
    }
  ]
}
```

## 常见问题排查

### 1. SQL 语法错误
如果出现 SQL 语法错误，检查：
- XML 中的特殊字符是否正确转义（`&gt;`, `&lt;`）
- CTE 语法是否正确
- 字段名是否匹配数据库表结构

### 2. 字段不存在错误
如果出现字段不存在错误，检查：
- `tenant_id` 和 `warehouse_id` 是否在所有 CTE 步骤中都被选择
- 字段别名是否正确

### 3. 缓存数据为空
如果缓存数据为空，检查：
- ProductCacheUtil 和 BinLocationCacheUtil 是否正常工作
- 缓存中是否有对应的数据

### 4. 权限问题
如果出现权限错误，检查：
- 用户是否有访问该仓库的权限
- tenant_id 是否正确设置

## 性能优化建议

1. **数据库索引**：确保以下字段有适当的索引
   - `product_id`
   - `create_time`
   - `remove_flag`

2. **查询优化**：
   - 限制查询的时间范围
   - 考虑添加 LIMIT 子句（如果需要）

3. **缓存优化**：
   - 批量查询缓存数据
   - 考虑缓存预热

## 部署注意事项

1. **数据库兼容性**：确认 MySQL 版本支持 CTE 语法（MySQL 8.0+）
2. **配置检查**：确认 MyBatis Plus 拦截器配置正确
3. **监控**：添加适当的日志和监控

## 总结

修复后的实现应该能够：
- ✅ 正确执行复杂的 CTE 查询
- ✅ 处理 tenant_id 和 warehouse_id 字段
- ✅ 计算 currentTotalInStockQty
- ✅ 填充缓存数据
- ✅ 返回正确格式的结果

如果测试过程中遇到问题，请检查上述验证点和排查步骤。
