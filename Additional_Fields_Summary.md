# 新增字段总结

## 概述

根据用户需求，在 BinLocationLog 链路查询功能中新增了两个字段：`product_id` 和 `binlocation_id`。

## 新增字段详情

### 1. productId
- **类型**: `Long`
- **描述**: 产品ID
- **来源**: 直接从 `bin_location_log` 表的 `product_id` 字段获取
- **用途**: 提供产品的唯一标识，便于前端进行产品相关操作

### 2. binlocationId
- **类型**: `Long`
- **描述**: 库位ID（bin_location表的ID）
- **来源**: 从 `bin_location_log` 表的 `source_bin_location_id` 或 `dest_bin_location_id` 字段获取
- **用途**: 提供库位的唯一标识，便于前端进行库位相关操作

## 字段区别说明

现在 VO 中有两个相似的字段，需要注意区别：

| 字段名 | 类型 | 描述 | 来源表 |
|--------|------|------|--------|
| `binLocationDetailId` | Long | 库位详情ID | bin_location_detail 表的 ID |
| `binLocationId` | Long | 库位ID（用于查询缓存） | bin_location_detail 表的 bin_location_id |
| `binlocationId` | Long | 库位ID（bin_location表的ID） | bin_location 表的 ID |

**说明**:
- `binLocationDetailId`: 指向具体的库位详情记录
- `binLocationId`: 用于查询库位缓存，获取库位名称等信息
- `binlocationId`: 库位表的主键ID，与 `binLocationId` 实际上是同一个值

## 修改的文件

### 1. Java 文件
- **BinLocationLogChainVO.java**: 添加了 `productId` 和 `binlocationId` 字段

### 2. XML 文件
- **BinLocationLogMapper.xml**: 在最终 SELECT 中添加了这两个字段的映射

### 3. 文档文件
- **BinLocationLogChain_API_Documentation.md**: 更新了响应示例和字段说明
- **BinLocationLogChain_Test_Guide.md**: 更新了预期结果示例

## SQL 映射

在 `BinLocationLogMapper.xml` 的最终 SELECT 中：

```sql
SELECT
    -- ... 其他字段 ...
    qwt.product_id AS productId,
    qwt.bin_location_id AS binlocationId
FROM qty_with_total qwt
```

## API 响应示例

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "productVersionId": 12345,
      "binLocationDetailId": 67890,
      "logId": 111,
      "qtySide": "source",
      "beforeQty": 100,
      "changeQty": -10,
      "afterQty": 90,
      "createTime": "2024-12-08T10:30:00",
      "prevAfterQty": 100,
      "isChainOk": 1,
      "changeType": "pick",
      "productRefNum": "SKU-XIONGGU-00004",
      "supplierSku": "SUPPLIER-SKU-001",
      "locationName": "A-01-01",
      "currentTotalInStockQty": 100,
      "binLocationId": 123,
      "productId": 12345,
      "binlocationId": 456
    }
  ]
}
```

## 使用场景

### 1. productId 的使用场景
- 前端需要跳转到产品详情页面
- 进行产品相关的操作（如查看产品信息、编辑产品等）
- 与其他产品相关的 API 进行交互

### 2. binlocationId 的使用场景
- 前端需要跳转到库位详情页面
- 进行库位相关的操作（如查看库位信息、编辑库位等）
- 与其他库位相关的 API 进行交互

## 注意事项

1. **字段命名**: `binlocationId` 使用了小写的 `l`，这是为了与现有的命名约定保持一致
2. **数据一致性**: `binLocationId` 和 `binlocationId` 在大多数情况下应该是相同的值
3. **向后兼容**: 新增字段不会影响现有的功能，保持向后兼容性

## 测试验证

在测试时需要验证：
1. 新增字段是否正确返回
2. 字段值是否与数据库中的值一致
3. 不同 `qtySide`（source/dest）的记录是否返回正确的库位ID

## 总结

通过新增这两个字段，API 的响应更加完整，为前端提供了更多的数据支持，便于进行产品和库位相关的操作。这些字段的添加不会影响现有功能，同时提升了 API 的实用性。
