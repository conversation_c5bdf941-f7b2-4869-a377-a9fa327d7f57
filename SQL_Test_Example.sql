-- BinLocationLog 链路查询 SQL 测试示例
-- 这个 SQL 演示了如何使用窗口函数计算 currentTotalInStockQty

-- 假设当前库存数量为 100，查询产品 ID 为 12345 的链路信息

/* ===== 1. 将 source / dest 拆成独立行 ===== */
WITH qty_union AS (
    /* ---------- source 侧 ---------- */
    SELECT
        l.id   AS log_id,
        l.product_version_id,
        l.product_id,
        l.source_bin_location_detail_id AS bin_location_detail_id,
        l.source_bin_location_id        AS bin_location_id,
        l.create_time,
        'source'                        AS qty_side,
        l.source_before_in_stock_qty    AS before_qty,
        l.source_change_in_stock_qty    AS change_qty,
        l.source_after_in_stock_qty     AS after_qty,
        l.change_type
    FROM   bin_location_log l
    WHERE l.remove_flag = 0
      AND l.product_id = 12345  -- 替换为实际的产品ID
      AND l.create_time <= '2024-12-08 10:30:00'  -- 替换为实际的查询时间
    UNION ALL
    /* ---------- dest 侧（若与 source 完全重复则排除） ---------- */
    SELECT
        l.id,
        l.product_version_id,
        l.product_id,
        l.dest_bin_location_detail_id,
        l.dest_bin_location_id,
        l.create_time,
        'dest',
        l.dest_before_in_stock_qty,
        l.dest_change_in_stock_qty,
        l.dest_after_in_stock_qty,
        l.change_type
    FROM   bin_location_log l
    WHERE  NOT (
               l.dest_bin_location_detail_id = l.source_bin_location_detail_id
           AND l.dest_change_in_stock_qty    = l.source_change_in_stock_qty
           )
           AND l.remove_flag = 0
           AND l.product_id = 12345  -- 替换为实际的产品ID
           AND l.create_time <= '2024-12-08 10:30:00'  -- 替换为实际的查询时间
)
/* ===== 2. 用窗口函数做链路校验 ===== */
, qty_chain AS (
    SELECT
        q.*,
        LAG(q.after_qty) OVER (
            PARTITION BY q.product_version_id, q.bin_location_detail_id
            ORDER BY     q.create_time, q.log_id, q.qty_side
        ) AS prev_after_qty,
        ROW_NUMBER() OVER (
            ORDER BY q.create_time DESC, q.log_id DESC, q.qty_side DESC
        ) AS row_num
    FROM qty_union q
)
/* ===== 3. 计算 currentTotalInStockQty ===== */
, qty_with_total AS (
    SELECT
        qc.*,
        CASE
            WHEN qc.prev_after_qty IS NULL      THEN NULL
            WHEN qc.prev_after_qty = qc.before_qty THEN 1
            ELSE 0
        END AS is_chain_ok,
        -- 计算当前总库存：当前库存 - 累计变更数量 + 当前行变更数量
        100 - SUM(qc.change_qty) OVER (
            ORDER BY qc.create_time DESC, qc.log_id DESC, qc.qty_side DESC
            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ) + qc.change_qty AS current_total_in_stock_qty
    FROM qty_chain qc
)
/* ===== 4. 最终结果（按 create_time desc, id desc 排序） ===== */
SELECT
    qwt.product_version_id,
    qwt.bin_location_detail_id,
    qwt.log_id,
    qwt.qty_side,
    qwt.before_qty,
    qwt.change_qty,
    qwt.after_qty,
    qwt.create_time,
    qwt.prev_after_qty,
    qwt.is_chain_ok,
    qwt.change_type,
    qwt.bin_location_id,
    qwt.current_total_in_stock_qty
FROM   qty_with_total qwt
ORDER BY
    qwt.create_time DESC,
    qwt.log_id     DESC;

-- 计算逻辑说明：
-- 假设有以下数据（按时间倒序）：
-- Row 1: change_qty = -10, current_total = 100 - (-10) + (-10) = 100
-- Row 2: change_qty = -5,  current_total = 100 - (-10 + -5) + (-5) = 90  
-- Row 3: change_qty = +20, current_total = 100 - (-10 + -5 + 20) + 20 = 95

-- 这样第一行显示的是当前库存（100），后续行显示的是执行该操作前的库存状态
