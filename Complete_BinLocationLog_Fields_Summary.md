# BinLocationLog 完整字段返回总结

## 概述

根据用户需求，现在 BinLocationLogChainVO 和相应的 SQL 查询已经包含了 `bin_location_log` 表的所有字段，方便前端显示完整的日志信息。

## 完整字段列表

### 1. 链路分析相关字段（原有）
| 字段名 | 类型 | 描述 |
|--------|------|------|
| productVersionId | Long | 产品版本ID |
| binLocationDetailId | Long | 库位详情ID |
| logId | Long | 日志ID |
| qtySide | String | 数量侧（source/dest） |
| beforeQty | Integer | 变更前数量 |
| changeQty | Integer | 变更数量 |
| afterQty | Integer | 变更后数量 |
| createTime | LocalDateTime | 创建时间 |
| prevAfterQty | Integer | 上一条记录的变更后数量 |
| isChainOk | Integer | 链路是否正常（1=正常，0=异常） |
| changeType | String | 变更类型 |
| currentTotalInStockQty | Integer | 当前总库存数量（计算值） |

### 2. 缓存数据字段（原有）
| 字段名 | 类型 | 描述 |
|--------|------|------|
| productRefNum | String | 产品编号（从缓存获取） |
| supplierSku | String | 供应商SKU（从缓存获取） |
| locationName | String | 库位名称（从缓存获取） |

### 3. 基础标识字段（新增）
| 字段名 | 类型 | 描述 |
|--------|------|------|
| productId | Long | 产品ID |
| binLocationId | Long | 库位ID |
| binlocationId | Long | 库位ID（bin_location表的ID） |

### 4. BinLocationLog 表的所有原始字段（新增）

#### 4.1 基础字段
| 字段名 | 类型 | 描述 |
|--------|------|------|
| version | Long | 版本号 |
| createBy | Long | 创建人 |
| updateBy | Long | 更新人 |
| updateTime | LocalDateTime | 更新时间 |
| removeFlag | Integer | 删除标记 |

#### 4.2 关联表信息
| 字段名 | 类型 | 描述 |
|--------|------|------|
| refTableId | Long | 关联表ID |
| refTableName | String | 关联表名称 |
| refTableRefNum | String | 关联表编号 |
| refTableShowName | String | 关联表显示名称 |
| refTableShowRefNum | String | 关联表显示编号 |

#### 4.3 源库位信息
| 字段名 | 类型 | 描述 |
|--------|------|------|
| sourceBinLocationId | Long | 源库位ID |
| sourceBinLocationDetailId | Long | 源库位详情ID |
| sourceChangeInStockQty | Integer | 源库位变更数量 |
| sourceBeforeInStockQty | Integer | 源库位变更前数量 |
| sourceAfterInStockQty | Integer | 源库位变更后数量 |

#### 4.4 目标库位信息
| 字段名 | 类型 | 描述 |
|--------|------|------|
| destBinLocationId | Long | 目标库位ID |
| destBinLocationDetailId | Long | 目标库位详情ID |
| destChangeInStockQty | Integer | 目标库位变更数量 |
| destBeforeInStockQty | Integer | 目标库位变更前数量 |
| destAfterInStockQty | Integer | 目标库位变更后数量 |

#### 4.5 其他信息
| 字段名 | 类型 | 描述 |
|--------|------|------|
| note | String | 备注 |
| deletedNote | String | 删除备注 |
| tenantId | Long | 租户ID |
| warehouseId | Long | 仓库ID |

## 修改的文件

### 1. Java 文件
- **BinLocationLogChainVO.java**: 添加了所有 BinLocationLog 表的字段

### 2. XML 文件
- **BinLocationLogMapper.xml**: 
  - 在 CTE 的 source 和 dest 侧都添加了所有字段
  - 在最终 SELECT 中添加了所有字段的映射

### 3. 文档文件
- **BinLocationLogChain_API_Documentation.md**: 更新了字段说明表格

## 字段数量统计

- **总字段数**: 约 40+ 个字段
- **链路分析字段**: 12 个
- **缓存数据字段**: 3 个
- **基础标识字段**: 3 个
- **BinLocationLog 原始字段**: 25 个

## 前端使用建议

### 1. 显示优先级
**高优先级**（主要显示）:
- logId, createTime, changeType
- qtySide, beforeQty, changeQty, afterQty
- isChainOk, productRefNum, locationName

**中优先级**（详情显示）:
- refTableName, refTableShowName
- sourceBinLocationId, destBinLocationId
- note, createBy, updateBy

**低优先级**（调试/管理用）:
- version, removeFlag, tenantId, warehouseId
- deletedNote, refTableId

### 2. 数据处理建议
- **qtySide**: 用于区分是源库位还是目标库位的操作
- **isChainOk**: 用于高亮显示链路异常的记录
- **currentTotalInStockQty**: 显示计算出的当前总库存
- **source/dest 字段**: 根据 qtySide 选择显示对应的字段

### 3. 界面布局建议
```
主要信息区域:
- 时间、操作类型、产品信息
- 数量变更信息、链路状态

详情信息区域:
- 关联单据信息
- 源/目标库位详情
- 备注信息

调试信息区域（可折叠）:
- 系统字段（版本、租户等）
- 原始数据字段
```

## 注意事项

1. **性能考虑**: 返回字段较多，建议前端按需显示
2. **数据一致性**: source/dest 字段在不同 qtySide 下含义不同
3. **空值处理**: 某些字段在特定操作类型下可能为空
4. **权限控制**: 某些敏感字段（如 tenantId）可能需要权限控制

## 总结

现在 API 返回了 BinLocationLog 表的完整信息，为前端提供了最大的灵活性。前端可以根据实际需求选择显示哪些字段，同时也为后续的功能扩展提供了充分的数据支持。
