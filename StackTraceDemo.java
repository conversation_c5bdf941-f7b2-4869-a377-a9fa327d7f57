/**
 * Stack trace optimization demo
 */
public class StackTraceDemo {
    
    public static void main(String[] args) {
        System.out.println("=== Stack Trace Optimization Demo ===\n");
        
        try {
            simulateException();
        } catch (Exception e) {
            System.out.println("Before optimization (verbose):");
            e.printStackTrace();
            
            System.out.println("\n" + "=".repeat(60) + "\n");
            
            System.out.println("After optimization (compact):");
            String compactStack = buildCompactStackTrace(e);
            System.out.println("Exception: " + e.getClass().getSimpleName());
            System.out.println("Message: " + e.getMessage());
            System.out.println("Stack: " + compactStack);
        }
    }
    
    private static void simulateException() {
        businessMethod();
    }
    
    private static void businessMethod() {
        serviceMethod();
    }
    
    private static void serviceMethod() {
        daoMethod();
    }
    
    private static void daoMethod() {
        String data = null;
        data.length(); // NPE here
    }
    
    private static String buildCompactStackTrace(Exception ex) {
        if (ex == null || ex.getStackTrace() == null || ex.getStackTrace().length == 0) {
            return "";
        }
        
        StringBuilder compactStack = new StringBuilder();
        StackTraceElement[] stackTrace = ex.getStackTrace();
        int relevantCount = 0;
        
        for (StackTraceElement element : stackTrace) {
            if (relevantCount >= 5) {
                break;
            }
            
            String className = element.getClassName();
            
            if (isRelevantStackTrace(className)) {
                if (compactStack.length() > 0) {
                    compactStack.append(" -> ");
                }
                
                String simplifiedClassName = simplifyClassName(className);
                compactStack.append(simplifiedClassName)
                          .append(".")
                          .append(element.getMethodName())
                          .append(":")
                          .append(element.getLineNumber());
                
                relevantCount++;
            }
        }
        
        return compactStack.toString();
    }
    
    private static boolean isRelevantStackTrace(String className) {
        if (className == null || className.trim().isEmpty()) {
            return false;
        }
        
        if (className.contains("StackTraceDemo") || 
            className.startsWith("cn.need.")) {
            return true;
        }
        
        return !className.startsWith("org.springframework.") &&
               !className.startsWith("org.apache.") &&
               !className.startsWith("com.sun.") &&
               !className.startsWith("sun.") &&
               !className.startsWith("java.") &&
               !className.startsWith("javax.");
    }
    
    private static String simplifyClassName(String fullClassName) {
        if (fullClassName == null || fullClassName.trim().isEmpty()) {
            return fullClassName;
        }
        
        String[] parts = fullClassName.split("\\.");
        if (parts.length <= 2) {
            return fullClassName;
        }
        
        return parts[parts.length - 2] + "." + parts[parts.length - 1];
    }
}
