package cn.need.cloud.apicenter.frp.rest.test;

import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 日志测试控制器
 * 用于测试 frp-api-center-server 的日志记录功能
 * 验证"执行请求"、"请求完成"和失败响应返回值记录
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-08
 */
@Slf4j
@RestController
@RequestMapping("/api/test/log")
@Tag(name = "日志测试API", description = "用于测试日志记录功能的API接口")
public class LogTestController extends AbstractController {

    /**
     * 测试成功响应的日志记录
     * 应该记录"执行请求"和"请求完成"日志
     */
    @Operation(summary = "测试成功响应日志")
    @GetMapping("/success")
    public Result<String> testSuccessLog(@RequestParam(defaultValue = "test") String message) {
        log.info("处理成功请求: {}", message);
        return success("请求处理成功: " + message);
    }

    /**
     * 测试业务异常的日志记录
     * 应该记录"执行请求"、异常处理和失败响应返回值
     */
    @Operation(summary = "测试业务异常日志")
    @GetMapping("/business-error")
    public Result<String> testBusinessErrorLog(@RequestParam(defaultValue = "测试业务异常") String errorMessage) {
        log.info("准备抛出业务异常: {}", errorMessage);
        throw new BusinessException("业务异常: " + errorMessage);
    }

    /**
     * 测试系统异常的日志记录
     * 应该记录"执行请求"、异常处理和失败响应返回值
     */
    @Operation(summary = "测试系统异常日志")
    @GetMapping("/system-error")
    public Result<String> testSystemErrorLog() {
        log.info("准备抛出系统异常");
        throw new RuntimeException("这是一个测试系统异常");
    }

    /**
     * 测试POST请求的日志记录
     * 验证请求体的记录功能
     */
    @Operation(summary = "测试POST请求日志")
    @PostMapping("/post-test")
    public Result<String> testPostLog(@RequestBody TestRequest request) {
        log.info("处理POST请求: {}", request);

        if ("error".equals(request.getType())) {
            throw new BusinessException("POST请求处理失败: " + request.getMessage());
        }

        return success("POST请求处理成功: " + request.getMessage());
    }

    /**
     * 测试缺少请求体的错误处理
     * 专门用于验证 "Required request body is missing" 错误的日志记录
     */
    @Operation(summary = "测试缺少请求体错误")
    @PostMapping("/missing-body-test")
    public Result<String> testMissingBodyLog(@RequestBody TestRequest request) {
        log.info("处理需要请求体的POST请求: {}", request);
        return success("请求体处理成功: " + request.getMessage());
    }

    /**
     * 测试缺少必需参数的错误处理
     */
    @Operation(summary = "测试缺少必需参数错误")
    @GetMapping("/missing-param-test")
    public Result<String> testMissingParamLog(@RequestParam String requiredParam) {
        log.info("处理需要必需参数的GET请求: {}", requiredParam);
        return success("必需参数处理成功: " + requiredParam);
    }

    /**
     * 测试请求对象
     */
    public static class TestRequest {
        private String type;
        private String message;

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        @Override
        public String toString() {
            return "TestRequest{" +
                    "type='" + type + '\'' +
                    ", message='" + message + '\'' +
                    '}';
        }
    }
}
