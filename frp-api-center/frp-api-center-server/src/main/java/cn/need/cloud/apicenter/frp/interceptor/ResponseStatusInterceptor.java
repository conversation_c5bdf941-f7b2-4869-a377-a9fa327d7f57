package cn.need.cloud.apicenter.frp.interceptor;

import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.text.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * API响应状态记录拦截器
 * 专门用于记录发生异常的请求，不再基于HTTP状态码判断。
 * 失败响应的判断现在由 ResponseLoggingAdvice 基于 Result.success 字段处理。
 *
 * <AUTHOR>
 * @since 2025-01-08
 * @version 1.0.0
 */
@Slf4j
@Component
public class ResponseStatusInterceptor implements HandlerInterceptor {

    /**
     * 请求完成后的处理方法，专门用于记录发生异常的请求。
     * 不再基于HTTP状态码判断，只记录真正发生异常的情况。
     * 失败响应的判断现在由 ResponseLoggingAdvice 基于 Result.success 字段处理。
     *
     * @param request  当前HTTP请求对象
     * @param response 当前HTTP响应对象
     * @param handler  选择用于执行请求的处理器对象
     * @param ex       处理过程中发生的异常，如果没有异常则为null
     */
    @Override
    public void afterCompletion(@NonNull HttpServletRequest request,
                              @NonNull HttpServletResponse response,
                              @NonNull Object handler,
                              Exception ex) {

        // 只记录真正发生异常的情况
        if (ex != null) {
            String traceId = MDC.get("traceId");
            String traceInfo = StringUtil.isNotBlank(traceId) ? " [TraceId: " + traceId + "]" : "";
            
            // 构建异常响应日志信息 - 使用单行格式避免ES采集分割
            StringBuilder logMessage = new StringBuilder();
            logMessage.append("<================================  请求异常   ================================>").append(traceInfo);
            logMessage.append(" | Status: ").append(response.getStatus());
            logMessage.append(" | Method: ").append(request.getMethod());
            logMessage.append(" | URI: ").append(request.getRequestURI());
            
            // 添加查询参数
            if (StringUtil.isNotBlank(request.getQueryString())) {
                logMessage.append("?").append(request.getQueryString());
            }
            
            // 添加请求参数
            if (!request.getParameterMap().isEmpty()) {
                logMessage.append(" | Request Parameters: ").append(JsonUtil.toJson(request.getParameterMap()));
            }
            
            // 添加异常信息
            logMessage.append(" | Exception: ").append(ex.getClass().getSimpleName());
            logMessage.append(" | Exception Message: ").append(ex.getMessage() != null ? ex.getMessage().replace("\n", "").replace("\r", "") : "null");

            // 添加简约的堆栈信息
            String compactStackTrace = buildCompactStackTrace(ex);
            if (StringUtil.isNotBlank(compactStackTrace)) {
                logMessage.append(" | Stack: ").append(compactStackTrace);
            }

            logMessage.append(" | ").append("<================================  请求异常   ================================>").append(traceInfo);

            // 记录异常响应日志
            log.error(logMessage.toString());
        }
    }

    /**
     * 构建简约的堆栈信息，便于快速定位问题
     * 只包含项目相关的堆栈信息，过滤掉框架和第三方库的堆栈
     *
     * @param ex 异常对象
     * @return 简约的堆栈信息字符串
     */
    private String buildCompactStackTrace(Exception ex) {
        if (ex == null || ex.getStackTrace() == null || ex.getStackTrace().length == 0) {
            return "";
        }

        StringBuilder compactStack = new StringBuilder();
        StackTraceElement[] stackTrace = ex.getStackTrace();
        int relevantCount = 0;

        // 只取前5个相关的堆栈信息，避免日志过长
        for (StackTraceElement element : stackTrace) {
            if (relevantCount >= 5) {
                break;
            }

            String className = element.getClassName();

            // 只记录项目相关的堆栈信息，过滤掉框架和第三方库
            if (isRelevantStackTrace(className)) {
                if (compactStack.length() > 0) {
                    compactStack.append(" -> ");
                }

                // 简化类名，只保留最后两级
                String simplifiedClassName = simplifyClassName(className);
                compactStack.append(simplifiedClassName)
                          .append(".")
                          .append(element.getMethodName())
                          .append(":")
                          .append(element.getLineNumber());

                relevantCount++;
            }
        }

        return compactStack.toString();
    }

    /**
     * 判断是否是相关的堆栈信息
     * 只保留项目代码的堆栈，过滤掉框架和第三方库
     *
     * @param className 类名
     * @return 是否相关
     */
    private boolean isRelevantStackTrace(String className) {
        if (StringUtil.isBlank(className)) {
            return false;
        }

        // 包含项目包名的堆栈
        if (className.startsWith("cn.need.")) {
            return true;
        }

        // 过滤掉框架和第三方库的堆栈
        return !className.startsWith("org.springframework.") &&
               !className.startsWith("org.apache.") &&
               !className.startsWith("com.sun.") &&
               !className.startsWith("sun.") &&
               !className.startsWith("java.") &&
               !className.startsWith("javax.") &&
               !className.startsWith("org.eclipse.") &&
               !className.startsWith("org.junit.") &&
               !className.startsWith("com.intellij.") &&
               !className.startsWith("org.mybatis.") &&
               !className.startsWith("com.baomidou.") &&
               !className.startsWith("org.slf4j.") &&
               !className.startsWith("ch.qos.logback.");
    }

    /**
     * 简化类名，只保留最后两级包名
     *
     * @param fullClassName 完整类名
     * @return 简化后的类名
     */
    private String simplifyClassName(String fullClassName) {
        if (StringUtil.isBlank(fullClassName)) {
            return fullClassName;
        }

        String[] parts = fullClassName.split("\\.");
        if (parts.length <= 2) {
            return fullClassName;
        }

        // 保留最后两级：包名.类名
        return parts[parts.length - 2] + "." + parts[parts.length - 1];
    }
}
