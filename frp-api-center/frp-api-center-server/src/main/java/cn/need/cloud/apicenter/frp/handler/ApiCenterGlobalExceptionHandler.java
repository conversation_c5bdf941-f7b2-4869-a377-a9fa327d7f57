package cn.need.cloud.apicenter.frp.handler;

import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.handler.GlobalExceptionAdvice;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * API中心全局异常处理器
 * 继承通用异常处理器，添加特定的日志记录功能。
 * 重点记录失败响应的返回值，满足"请求完成如果不是 success，需要记录一下返回值"的需求。
 *
 * <AUTHOR>
 * @since 2025-01-08
 * @version 1.0.0
 */
@Slf4j
@Order(-200)
@RestControllerAdvice
public class ApiCenterGlobalExceptionHandler extends GlobalExceptionAdvice {

    /**
     * 处理缺少请求体异常
     * 专门处理 "Required request body is missing" 错误
     *
     * @param request  请求对象
     * @param response 响应对象
     * @param e        异常信息
     * @return Result 统一响应结果
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<String> handleHttpMessageNotReadableException(HttpServletRequest request, HttpServletResponse response, HttpMessageNotReadableException e) {
        // 调用父类方法处理异常
        Result<String> result = super.handleException(request, response, e);

        // 记录失败响应的返回值
        recordFailureResponse(request, result, "缺少请求体或请求体格式错误");

        return result;
    }

    /**
     * 处理参数验证异常
     * 专门处理方法参数验证失败的情况
     *
     * @param request  请求对象
     * @param response 响应对象
     * @param e        异常信息
     * @return Result 统一响应结果
     */
    @Override
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<String> handleValidationException(HttpServletRequest request, HttpServletResponse response, MethodArgumentNotValidException e) {
        // 调用父类方法处理异常
        Result<String> result = super.handleValidationException(request, response, e);

        // 记录失败响应的返回值
        recordFailureResponse(request, result, "参数验证失败");

        return result;
    }

    /**
     * 处理缺少请求参数异常
     *
     * @param request  请求对象
     * @param response 响应对象
     * @param e        异常信息
     * @return Result 统一响应结果
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<String> handleMissingServletRequestParameterException(HttpServletRequest request, HttpServletResponse response, MissingServletRequestParameterException e) {
        // 调用父类方法处理异常
        Result<String> result = super.handleException(request, response, e);

        // 记录失败响应的返回值
        recordFailureResponse(request, result, "缺少必需的请求参数");

        return result;
    }

    /**
     * 处理不支持的请求方法异常
     *
     * @param request  请求对象
     * @param response 响应对象
     * @param e        异常信息
     * @return Result 统一响应结果
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<String> handleHttpRequestMethodNotSupportedException(HttpServletRequest request, HttpServletResponse response, HttpRequestMethodNotSupportedException e) {
        // 调用父类方法处理异常
        Result<String> result = super.handleException(request, response, e);

        // 记录失败响应的返回值
        recordFailureResponse(request, result, "不支持的请求方法");

        return result;
    }

    /**
     * 重写异常处理方法，添加返回值记录功能
     * 在处理异常的同时，记录失败响应的返回值
     *
     * @param request  请求对象
     * @param response 响应对象
     * @param e        异常信息
     * @return Result 统一响应结果
     */
    @Override
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<String> handleException(HttpServletRequest request, HttpServletResponse response, Exception e) {
        // 调用父类方法处理异常
        Result<String> result = super.handleException(request, response, e);

        // 记录失败响应的返回值
        recordFailureResponse(request, result, "通用异常处理");

        return result;
    }

    /**
     * 记录失败响应的返回值（重载方法，使用默认错误类型）
     * 专门用于记录请求完成时非成功状态的返回值信息
     *
     * @param request 请求对象
     * @param result  响应结果
     */
    private void recordFailureResponse(HttpServletRequest request, Result<String> result) {
        recordFailureResponse(request, result, "未知错误类型");
    }

    /**
     * 记录失败响应的返回值
     * 专门用于记录请求完成时非成功状态的返回值信息
     *
     * @param request   请求对象
     * @param result    响应结果
     * @param errorType 错误类型描述
     */
    private void recordFailureResponse(HttpServletRequest request, Result<String> result, String errorType) {
        try {
            String traceId = MDC.get("traceId");
            String traceInfo = StringUtil.isNotBlank(traceId) ? " [TraceId: " + traceId + "]" : "";
            
            // 构建失败响应返回值日志 - 使用单行格式避免ES采集分割
            StringBuilder logMessage = new StringBuilder();
            logMessage.append("<================================  失败响应返回值   ================================>").append(traceInfo);
            logMessage.append(" | Error Type: ").append(errorType);
            logMessage.append(" | API Path: ").append(request.getRequestURI());
            logMessage.append(" | Method: ").append(request.getMethod());
            logMessage.append(" | Response Code: ").append(result.getCode());
            logMessage.append(" | Response Message: ").append(result.getMessage() != null ? result.getMessage().replace("\n", "").replace("\r", "") : "null");
            logMessage.append(" | Response Success: ").append(result.isSuccess());
            
            // 记录完整的返回值JSON
            String responseJson = JsonUtil.toJson(result);
            if (StringUtil.isNotBlank(responseJson)) {
                // 清理换行符，避免多行日志
                String cleanResponseJson = responseJson.replace("\n", "").replace("\r", "");
                logMessage.append(" | Full Response: ").append(cleanResponseJson);
            }
            
            logMessage.append(" | ").append("<================================  失败响应返回值   ================================>").append(traceInfo);
            
            // 记录失败响应返回值日志
            log.error(logMessage.toString());
            
        } catch (Exception ex) {
            // 记录日志时发生异常，不影响主流程
            log.warn("记录失败响应返回值时发生异常: {}", ex.getMessage());
        }
    }
}
