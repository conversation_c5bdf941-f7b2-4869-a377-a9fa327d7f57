package cn.need.cloud.apicenter.frp.advice;

import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.support.api.Result;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 * 响应体日志记录增强器
 * 用于拦截所有响应，检查 Result 对象的 success 字段，
 * 当 success = false 时记录"请求完成(失败)"日志。
 * 这样可以确保所有失败的请求（包括参数验证失败、缺少请求体等）都能被正确记录。
 * 
 * <AUTHOR>
 * @since 2025-01-08
 * @version 1.0.0
 */
@Slf4j
@RestControllerAdvice
public class ResponseLoggingAdvice implements ResponseBodyAdvice<Object> {

    /**
     * 判断是否需要处理响应体
     * 只处理返回 Result 类型的响应
     *
     * @param returnType    返回类型
     * @param converterType 转换器类型
     * @return 是否需要处理
     */
    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 只处理 Result 类型的响应
        Class<?> parameterType = returnType.getParameterType();
        return Result.class.isAssignableFrom(parameterType);
    }

    /**
     * 在响应体写入之前进行处理
     * 检查 Result 对象的 success 字段，如果为 false 则记录失败日志
     *
     * @param body                  响应体
     * @param returnType            返回类型
     * @param selectedContentType   选择的内容类型
     * @param selectedConverterType 选择的转换器类型
     * @param request               请求对象
     * @param response              响应对象
     * @return 处理后的响应体
     */
    @Override
    public Object beforeBodyWrite(Object body, 
                                MethodParameter returnType, 
                                MediaType selectedContentType,
                                Class<? extends HttpMessageConverter<?>> selectedConverterType, 
                                ServerHttpRequest request,
                                ServerHttpResponse response) {
        
        // 只处理 Result 类型的响应
        if (body instanceof Result) {
            Result<?> result = (Result<?>) body;
            
            // 如果 success = false，记录失败日志
            if (!result.isSuccess()) {
                recordFailureCompletion(request, result);
            }
        }
        
        return body;
    }

    /**
     * 记录请求完成(失败)日志
     * 基于 Result 对象的 success 字段判断，而不是 HTTP 状态码
     *
     * @param request 请求对象
     * @param result  响应结果
     */
    private void recordFailureCompletion(ServerHttpRequest request, Result<?> result) {
        try {
            // 获取 HttpServletRequest
            HttpServletRequest httpRequest = null;
            if (request instanceof ServletServerHttpRequest) {
                httpRequest = ((ServletServerHttpRequest) request).getServletRequest();
            }
            
            if (httpRequest == null) {
                return;
            }
            
            String traceId = MDC.get("traceId");
            String traceInfo = StringUtil.isNotBlank(traceId) ? " [TraceId: " + traceId + "]" : "";
            
            // 构建失败响应日志信息 - 使用单行格式避免ES采集分割
            StringBuilder logMessage = new StringBuilder();
            logMessage.append("<================================  请求完成(失败)   ================================>").append(traceInfo);
            logMessage.append(" | Method: ").append(httpRequest.getMethod());
            logMessage.append(" | URI: ").append(httpRequest.getRequestURI());
            
            // 添加查询参数
            if (StringUtil.isNotBlank(httpRequest.getQueryString())) {
                logMessage.append("?").append(httpRequest.getQueryString());
            }
            
            // 添加请求参数
            if (!httpRequest.getParameterMap().isEmpty()) {
                logMessage.append(" | Request Parameters: ").append(JsonUtil.toJson(httpRequest.getParameterMap()));
            }
            
            // 添加响应信息
            logMessage.append(" | Response Code: ").append(result.getCode());
            logMessage.append(" | Response Message: ").append(result.getMessage() != null ? result.getMessage().replace("\n", "").replace("\r", "") : "null");
            logMessage.append(" | Response Success: ").append(result.isSuccess());
            
            // 添加完整响应体（用于失败分析）
            try {
                String fullResponse = JsonUtil.toJson(result);
                // 清理换行符，避免多行日志
                String cleanResponse = fullResponse.replace("\n", "").replace("\r", "");
                logMessage.append(" | Full Response: ").append(cleanResponse);
            } catch (Exception e) {
                logMessage.append(" | Full Response: [JSON序列化失败: ").append(e.getMessage()).append("]");
            }
            
            logMessage.append(" | ").append("<================================  请求完成(失败)   ================================>").append(traceInfo);
            
            // 记录失败响应日志
            log.warn(logMessage.toString());
            
        } catch (Exception e) {
            // 确保日志记录不会影响正常的响应处理
            log.error("记录失败响应日志时发生异常: {}", e.getMessage(), e);
        }
    }
}
