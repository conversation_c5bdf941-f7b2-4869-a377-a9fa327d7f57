# frp-api-center 应用配置
# 配置请求日志记录，实现与 frp-business 相同的日志记录模式

# 请求日志配置
servlet:
  request:
    log:
      enable: true                    # 启用请求日志
      include-query-string: true      # 包含查询参数
      include-headers: false          # 包含请求头
      include-payload: true           # 包含请求体
      include-client-info: false      # 包含客户端信息
      truncation-length: -1           # 截断长度，-1表示不截断
      ignored-paths:                  # 忽略的路径列表，不记录日志
        - /actuator/health
        - /actuator/info

# 日志级别配置
logging:
  level:
    # 启用请求日志记录
    cn.need.framework.starter.web.log.RequestLoggingFilter: DEBUG
    # 启用API中心相关日志
    cn.need.cloud.apicenter: DEBUG
    # 启用全局异常处理器日志
    cn.need.framework.common.support.handler.GlobalExceptionAdvice: DEBUG
    # 启用响应状态拦截器日志
    cn.need.cloud.apicenter.frp.interceptor.ResponseStatusInterceptor: DEBUG
    # 启用API中心全局异常处理器日志
    cn.need.cloud.apicenter.frp.handler.ApiCenterGlobalExceptionHandler: DEBUG
