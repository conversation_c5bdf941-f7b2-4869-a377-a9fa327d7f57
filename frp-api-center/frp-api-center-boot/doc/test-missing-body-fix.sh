#!/bin/bash

# frp-api-center-server 缺少请求体错误修复验证脚本
# 用于验证 "Required request body is missing" 错误的日志记录修复

echo "=========================================="
echo "frp-api-center-server 缺少请求体错误修复验证"
echo "=========================================="

# 服务器地址
SERVER_URL="http://localhost:10140"

echo ""
echo "1. 测试缺少请求体错误 (应该记录到请求完成日志)"
echo "请求: POST /api/test/log/missing-body-test (无请求体)"
echo "------------------------------------------"
curl -X POST "${SERVER_URL}/api/test/log/missing-body-test" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo ""

echo ""
echo "2. 测试缺少必需参数错误 (应该记录到请求完成日志)"
echo "请求: GET /api/test/log/missing-param-test (无必需参数)"
echo "------------------------------------------"
curl "${SERVER_URL}/api/test/log/missing-param-test" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo ""

echo ""
echo "3. 测试错误格式请求体 (应该记录到请求完成日志)"
echo "请求: POST /api/test/log/missing-body-test (错误JSON格式)"
echo "------------------------------------------"
curl -X POST "${SERVER_URL}/api/test/log/missing-body-test" \
  -H "Content-Type: application/json" \
  -d '{"invalid": json}' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo ""

echo ""
echo "4. 对比测试：正常请求 (应该记录成功日志)"
echo "请求: POST /api/test/log/post-test (正常请求体)"
echo "------------------------------------------"
curl -X POST "${SERVER_URL}/api/test/log/post-test" \
  -H "Content-Type: application/json" \
  -d '{"type":"success","message":"测试正常请求"}' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo ""

echo ""
echo "=========================================="
echo "测试完成！"
echo "=========================================="
echo ""
echo "请检查应用日志，确认以下内容："
echo "1. 所有请求都有 '执行请求' 日志"
echo "2. 错误请求有 '失败响应返回值' 日志"
echo "3. 错误请求有 '请求完成(失败)' 日志"
echo "4. 所有日志都包含 TraceId"
echo "5. 错误类型正确标识 (缺少请求体或请求体格式错误/缺少必需的请求参数)"
echo ""
echo "预期的日志格式示例："
echo "<================================  执行请求   ================================> [TraceId: xxx] ..."
echo "<================================  失败响应返回值   ================================> [TraceId: xxx] | Error Type: 缺少请求体或请求体格式错误 ..."
echo "<================================  请求完成(失败)   ================================> [TraceId: xxx] | Method: POST | URI: /api/test/log/missing-body-test | Response Code: 400 | Response Success: false ..."
echo ""
echo "重要更新: 现在使用 ResponseLoggingAdvice 基于 Result.success 字段判断失败，而不是 HTTP 状态码"
echo "这样可以正确处理业务异常等 HTTP 200 但 success=false 的情况"
