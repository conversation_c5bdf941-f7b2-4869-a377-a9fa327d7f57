# frp-api-center-server 日志记录实现指南

## 概述

本文档描述了 frp-api-center-server 项目中实现的日志记录功能，使其与 frp-business 项目保持一致的日志记录模式。

## 实现的功能

### 1. 统一的请求日志记录
- **执行请求**：记录请求开始时的信息
- **请求完成**：记录请求结束时的信息，包括耗时
- **TraceId 支持**：所有日志包含分布式链路追踪ID

### 2. 失败响应记录
- **异常处理**：记录异常详细信息
- **返回值记录**：当请求完成不是 success 时，记录完整的返回值
- **状态码记录**：记录HTTP状态码和响应状态

### 3. 日志格式统一
- **单行格式**：避免ES采集时的日志分割问题
- **TraceId 标识**：便于分布式系统问题排查
- **结构化信息**：包含请求方法、URI、参数等关键信息

## 新增文件

### 1. 配置类
- `WebConfig.java` - Web配置类，注册拦截器
- `application.yml` - 应用配置，启用请求日志

### 2. 拦截器
- `ResponseStatusInterceptor.java` - 响应状态拦截器，记录失败响应

### 3. 异常处理器
- `ApiCenterGlobalExceptionHandler.java` - 全局异常处理器，记录失败返回值

### 4. 测试控制器
- `LogTestController.java` - 日志测试控制器，验证功能

## 日志示例

### 成功请求日志
```
<================================  执行请求   ================================> [TraceId: 12345] | Method: GET | URI: /api/test/log/success
<================================  请求完成   ================================> [TraceId: 12345] | 请求耗时: 25ms | 请求URL: GET /api/test/log/success
```

### 失败请求日志
```
<================================  执行请求   ================================> [TraceId: 12345] | Method: GET | URI: /api/test/log/business-error
<================================  Global Exception Handler   ================================> [TraceId: 12345] | API Path: /api/test/log/business-error | Request Method: GET | Exception: BusinessException
<================================  失败响应返回值   ================================> [TraceId: 12345] | API Path: /api/test/log/business-error | Response Code: BUSINESS_ERROR | Response Success: false | Full Response: {"code":"BUSINESS_ERROR","message":"业务异常: 测试","success":false,"data":null}
```

## 配置说明

### 请求日志配置
```yaml
servlet:
  request:
    log:
      enable: true                    # 启用请求日志
      include-query-string: true      # 包含查询参数
      include-payload: true           # 包含请求体
      truncation-length: -1           # 不截断日志
```

### 日志级别配置
```yaml
logging:
  level:
    cn.need.framework.starter.web.log.RequestLoggingFilter: DEBUG
    cn.need.cloud.apicenter: DEBUG
```

## 测试方法

### 1. 成功请求测试
```bash
curl "http://localhost:10140/api/test/log/success?message=测试成功"
```

### 2. 业务异常测试
```bash
curl "http://localhost:10140/api/test/log/business-error?errorMessage=测试异常"
```

### 3. POST请求测试
```bash
curl -X POST "http://localhost:10140/api/test/log/post-test" \
  -H "Content-Type: application/json" \
  -d '{"type":"success","message":"测试POST请求"}'
```

### 4. POST异常测试
```bash
curl -X POST "http://localhost:10140/api/test/log/post-test" \
  -H "Content-Type: application/json" \
  -d '{"type":"error","message":"测试POST异常"}'
```

## 与 frp-business 的一致性

### 相同点
1. **日志格式**：使用相同的分割线和格式
2. **TraceId 支持**：所有日志包含链路追踪ID
3. **请求生命周期**：记录执行请求和请求完成
4. **异常处理**：统一的异常日志记录

### 增强功能
1. **失败返回值记录**：专门记录失败响应的完整返回值
2. **响应状态拦截**：额外的失败状态记录
3. **测试接口**：提供完整的测试验证功能

## 注意事项

1. **日志级别**：确保相关包的日志级别设置为 DEBUG
2. **ES 采集**：使用单行格式避免日志分割问题
3. **性能影响**：请求日志会增加少量性能开销
4. **存储空间**：详细日志会占用更多存储空间

## 维护建议

1. **定期清理**：配置日志轮转和清理策略
2. **监控告警**：基于错误日志设置监控告警
3. **性能监控**：监控日志记录对性能的影响
4. **配置调优**：根据实际需要调整日志详细程度
