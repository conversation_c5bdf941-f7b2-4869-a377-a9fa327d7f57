# frp-api-center-server 日志记录功能测试指南

## 测试前准备

### 1. 启动应用
确保 frp-api-center-server 应用已启动，默认端口为 10140。

### 2. 检查日志配置
确认以下配置已生效：
- `application.yml` 中的请求日志配置
- 日志级别设置为 DEBUG
- RequestLoggingFilter 自动配置生效

## 测试用例

### 1. 成功请求测试

**测试目的**: 验证"执行请求"和"请求完成"日志记录

**测试命令**:
```bash
curl "http://localhost:10140/api/test/log/success?message=测试成功请求"
```

**预期日志输出**:
```
<================================  执行请求   ================================> [TraceId: xxx] | Method: GET | URI: /api/test/log/success?message=测试成功请求
<================================  请求完成   ================================> [TraceId: xxx] | 请求耗时: XXms | 请求URL: GET /api/test/log/success
```

### 2. 业务异常测试

**测试目的**: 验证异常处理和失败响应返回值记录

**测试命令**:
```bash
curl "http://localhost:10140/api/test/log/business-error?errorMessage=测试业务异常"
```

**预期日志输出**:
```
<================================  执行请求   ================================> [TraceId: xxx] | Method: GET | URI: /api/test/log/business-error?errorMessage=测试业务异常
<================================  Global Exception Handler   ================================> [TraceId: xxx] | API Path: /api/test/log/business-error | Request Method: GET | Exception: BusinessException
<================================  失败响应返回值   ================================> [TraceId: xxx] | API Path: /api/test/log/business-error | Response Code: 500 | Response Success: false | Full Response: {"code":500,"message":"业务异常: 测试业务异常","success":false,"data":null,"traceId":"xxx"}
<================================  请求完成(失败)   ================================> [TraceId: xxx] | Method: GET | URI: /api/test/log/business-error | Response Code: 500 | Response Message: 业务异常: 测试业务异常 | Response Success: false | Full Response: {"code":500,"message":"业务异常: 测试业务异常","success":false,"data":null,"traceId":"xxx"}
```

### 3. 系统异常测试

**测试命令**:
```bash
curl "http://localhost:10140/api/test/log/system-error"
```

**预期日志输出**: 类似业务异常，但异常类型为 RuntimeException

### 4. POST请求成功测试

**测试命令**:
```bash
curl -X POST "http://localhost:10140/api/test/log/post-test" \
  -H "Content-Type: application/json" \
  -d '{"type":"success","message":"测试POST请求成功"}'
```

**预期日志输出**:
```
<================================  执行请求   ================================> [TraceId: xxx] | Method: POST | URI: /api/test/log/post-test | Request Body: {"type":"success","message":"测试POST请求成功"}
<================================  请求完成   ================================> [TraceId: xxx] | 请求耗时: XXms | 请求URL: POST /api/test/log/post-test
```

### 5. POST请求异常测试

**测试命令**:
```bash
curl -X POST "http://localhost:10140/api/test/log/post-test" \
  -H "Content-Type: application/json" \
  -d '{"type":"error","message":"测试POST请求异常"}'
```

**预期日志输出**: 包含请求体和异常信息的完整日志

### 6. 缺少请求体错误测试

**测试目的**: 验证 "Required request body is missing" 错误的日志记录

**测试命令**:
```bash
curl -X POST "http://localhost:10140/api/test/log/missing-body-test" \
  -H "Content-Type: application/json"
```

**预期日志输出**:
```
<================================  执行请求   ================================> [TraceId: xxx] | Method: POST | URI: /api/test/log/missing-body-test
<================================  Global Exception Handler   ================================> [TraceId: xxx] | API Path: /api/test/log/missing-body-test | Request Method: POST | Exception: HttpMessageNotReadableException
<================================  失败响应返回值   ================================> [TraceId: xxx] | Error Type: 缺少请求体或请求体格式错误 | API Path: /api/test/log/missing-body-test | Method: POST | Response Code: 400 | Response Success: false | Full Response: {"code":400,"message":"Required request body is missing...","success":false,"data":null,"traceId":"xxx"}
<================================  请求完成(失败)   ================================> [TraceId: xxx] | Status: 200 | Method: POST | URI: /api/test/log/missing-body-test | Exception: HttpMessageNotReadableException | Exception Message: Required request body is missing...
```

### 7. 缺少必需参数错误测试

**测试目的**: 验证缺少必需参数错误的日志记录

**测试命令**:
```bash
curl "http://localhost:10140/api/test/log/missing-param-test"
```

**预期日志输出**:
```
<================================  执行请求   ================================> [TraceId: xxx] | Method: GET | URI: /api/test/log/missing-param-test
<================================  Global Exception Handler   ================================> [TraceId: xxx] | API Path: /api/test/log/missing-param-test | Request Method: GET | Exception: MissingServletRequestParameterException
<================================  失败响应返回值   ================================> [TraceId: xxx] | Error Type: 缺少必需的请求参数 | API Path: /api/test/log/missing-param-test | Method: GET | Response Code: 400 | Response Success: false | Full Response: {"code":400,"message":"Required request parameter 'requiredParam' for method parameter type String is not present","success":false,"data":null,"traceId":"xxx"}
<================================  请求完成(失败)   ================================> [TraceId: xxx] | Status: 200 | Method: GET | URI: /api/test/log/missing-param-test | Exception: MissingServletRequestParameterException | Exception Message: Required request parameter 'requiredParam' for method parameter type String is not present
```

### 8. 错误格式请求体测试

**测试目的**: 验证请求体格式错误的日志记录

**测试命令**:
```bash
curl -X POST "http://localhost:10140/api/test/log/missing-body-test" \
  -H "Content-Type: application/json" \
  -d '{"invalid": json}'
```

**预期日志输出**: 类似缺少请求体，但错误信息为 JSON 解析错误

## 验证要点

### 1. 日志格式检查
- ✅ 所有日志都是单行格式（无换行符）
- ✅ 包含 TraceId 标识
- ✅ 使用统一的分割线格式
- ✅ 包含关键请求信息（Method、URI、参数等）

### 2. 日志内容检查
- ✅ "执行请求"日志在请求开始时记录
- ✅ "请求完成"日志在请求结束时记录
- ✅ 失败请求记录完整的返回值
- ✅ 异常信息记录详细且清晰

### 3. 功能完整性检查
- ✅ GET 请求日志记录
- ✅ POST 请求日志记录（包含请求体）
- ✅ 成功响应日志记录
- ✅ 失败响应日志记录
- ✅ 异常处理日志记录

## 故障排除

### 1. 日志不显示
- 检查日志级别配置
- 确认 RequestLoggingFilter 是否启用
- 检查 need-starter-web 依赖

### 2. TraceId 缺失
- 检查 logback-spring.xml 配置
- 确认 MDC 设置正确

### 3. 多行日志问题
- 检查日志格式化代码
- 确认换行符已被清理

### 4. 返回值记录缺失
- 检查 ApiCenterGlobalExceptionHandler 是否生效
- 确认异常处理器优先级

## 性能监控

### 1. 日志记录性能
- 监控请求处理时间增加
- 检查日志文件大小增长
- 观察内存使用情况

### 2. 存储空间
- 配置日志轮转策略
- 设置日志保留期限
- 监控磁盘空间使用

## 与 frp-business 对比

### 相同功能
- ✅ "执行请求"日志记录
- ✅ "请求完成"日志记录
- ✅ TraceId 支持
- ✅ 统一日志格式

### 增强功能
- ✅ 失败响应返回值记录
- ✅ 详细的异常信息记录
- ✅ 响应状态拦截
- ✅ 完整的测试验证

## 重要更新 - 基于 Result.success 的失败判断

### 新的日志记录机制

从 2025-01-08 开始，frp-api-center-server 使用了更智能的失败响应判断机制：

1. **ResponseLoggingAdvice**: 新增的响应体拦截器，专门检查 `Result` 对象的 `success` 字段
2. **智能失败判断**: 不再仅依赖 HTTP 状态码，而是基于业务逻辑的 `success` 字段
3. **完整覆盖**: 能够正确处理以下情况：
   - HTTP 200 但 `success=false` 的业务异常
   - HTTP 400 的参数验证失败
   - HTTP 500 的系统异常
   - 缺少请求体、参数等框架级异常

### 日志链条

现在的完整日志链条为：
1. **执行请求** (RequestLoggingFilter)
2. **Global Exception Handler** (异常处理器，如果有异常)
3. **失败响应返回值** (ApiCenterGlobalExceptionHandler，如果有异常)
4. **请求完成(失败)** (ResponseLoggingAdvice，基于 `Result.success` 判断)

### 优势

- ✅ 准确识别业务失败（即使 HTTP 状态码是 200）
- ✅ 统一的失败响应格式
- ✅ 完整的错误信息记录
- ✅ 与现有非标准 REST API 兼容

## 总结

通过以上测试，可以验证 frp-api-center-server 已成功实现与 frp-business 一致的日志记录模式，并在失败响应返回值记录方面有所增强。新的基于 `Result.success` 的判断机制更适合项目的实际业务需求。
