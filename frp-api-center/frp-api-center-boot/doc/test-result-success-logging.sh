#!/bin/bash

# frp-api-center-server Result.success 字段日志记录验证脚本
# 用于验证基于 Result.success 字段的失败响应日志记录

echo "=========================================="
echo "frp-api-center-server Result.success 日志记录验证"
echo "=========================================="

# 服务器地址
SERVER_URL="http://localhost:10140"

echo ""
echo "1. 测试成功响应 (success=true, HTTP 200)"
echo "请求: GET /api/test/log/success"
echo "------------------------------------------"
curl "${SERVER_URL}/api/test/log/success?message=测试成功" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo ""

echo ""
echo "2. 测试业务异常 (success=false, HTTP 200)"
echo "请求: GET /api/test/log/business-error"
echo "------------------------------------------"
curl "${SERVER_URL}/api/test/log/business-error?errorMessage=测试业务异常" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo ""

echo ""
echo "3. 测试参数验证失败 (success=false, HTTP 400)"
echo "请求: GET /api/test/log/missing-param-test (无必需参数)"
echo "------------------------------------------"
curl "${SERVER_URL}/api/test/log/missing-param-test" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo ""

echo ""
echo "4. 测试缺少请求体 (success=false, HTTP 400)"
echo "请求: POST /api/test/log/missing-body-test (无请求体)"
echo "------------------------------------------"
curl -X POST "${SERVER_URL}/api/test/log/missing-body-test" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo ""

echo ""
echo "5. 测试系统异常 (success=false, HTTP 500)"
echo "请求: GET /api/test/log/system-error"
echo "------------------------------------------"
curl "${SERVER_URL}/api/test/log/system-error" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo ""

echo ""
echo "=========================================="
echo "测试完成！"
echo "=========================================="
echo ""
echo "请检查应用日志，验证以下行为："
echo ""
echo "成功响应 (success=true):"
echo "- 应该有: 执行请求 -> 请求完成"
echo "- 不应该有: 请求完成(失败)"
echo ""
echo "失败响应 (success=false):"
echo "- 应该有: 执行请求 -> [异常处理] -> 请求完成(失败)"
echo "- 请求完成(失败) 由 ResponseLoggingAdvice 基于 Result.success 字段触发"
echo ""
echo "关键验证点："
echo "1. 业务异常 (HTTP 200, success=false) 应该记录 '请求完成(失败)'"
echo "2. 参数验证失败 (HTTP 400, success=false) 应该记录 '请求完成(失败)'"
echo "3. 成功请求 (HTTP 200, success=true) 不应该记录 '请求完成(失败)'"
echo "4. 所有失败响应都应该包含完整的 Response 信息"
echo ""
echo "日志格式示例："
echo "成功: <================================  请求完成   ================================> [TraceId: xxx] | 请求耗时: XXms ..."
echo "失败: <================================  请求完成(失败)   ================================> [TraceId: xxx] | Method: GET | URI: ... | Response Code: XXX | Response Success: false ..."
echo ""
