package cn.need.framework.common.support.handler;

import cn.need.framework.common.core.exception.ExceptionUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.exception.unchecked.UncheckedException;
import cn.need.framework.common.core.http.HttpCode;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.support.api.Result;
import com.alibaba.excel.exception.ExcelAnalysisException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;

/**
 * Global Exception Handler
 *
 * <AUTHOR>
 */
@Slf4j
@Order(-100)
@RestControllerAdvice
public class GlobalExceptionAdvice {

    /**
     * Handle all uncaught exceptions
     *
     * @param request  Request object
     * @param response Response object
     * @param e        Exception information
     * @return Result Unified response result
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<String> handleException(HttpServletRequest request, HttpServletResponse response, Exception e) {
        // Get trace ID from MDC
        String traceId = MDC.get("traceId");
        String traceInfo = StringUtil.isNotBlank(traceId) ? " [TraceId: " + traceId + "]" : "";

        // Build comprehensive log message - 使用单行格式避免ES采集分割
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("<================================  Global Exception Handler   ================================>").append(traceInfo);
        logMessage.append(" | API Path: ").append(request.getRequestURI());
        logMessage.append(" | Request Method: ").append(request.getMethod());
        logMessage.append(" | Request Parameters: ").append(JsonUtil.toJson(request.getParameterMap()));

        // Get request body content
        String requestBody = getRequestBody(request);
        if (StringUtil.isNotBlank(requestBody)) {
            // 清理换行符，避免多行日志
            String cleanRequestBody = requestBody.replace("\n", "").replace("\r", "");
            logMessage.append(" | Request Body: ").append(cleanRequestBody);
        }

        logMessage.append(" | Exception Type: ").append(e.getClass().getSimpleName());
        logMessage.append(" | Exception Message: ").append(e.getMessage() != null ? e.getMessage().replace("\n", "").replace("\r", "") : "null");

        // 添加简约的堆栈信息
        String compactStackTrace = buildCompactStackTrace(e);
        if (StringUtil.isNotBlank(compactStackTrace)) {
            logMessage.append(" | Stack: ").append(compactStackTrace);
        }

        logMessage.append(" | ").append("<================================  Global Exception Handler   ================================>").append(traceInfo);

        // Log as single message without full stack trace
        log.error(logMessage.toString());

        // Get API path
        String apiPath = request.getRequestURI();

        // Build enhanced error message including API path
        String enhancedMessage = String.format("API Path: %s, Error Message: %s", apiPath, getErrorMessage(e));

        // If it's a null pointer exception, provide more detailed information
        if (e instanceof NullPointerException) {
            // Get the first element of the exception stack, which usually contains the class and method where the exception occurred
            StackTraceElement[] stackTrace = e.getStackTrace();
            if (stackTrace.length > 0) {
                StackTraceElement element = stackTrace[0];
                String className = element.getClassName();
                String methodName = element.getMethodName();
                int lineNumber = element.getLineNumber();

                enhancedMessage = String.format("API Path: %s, NullPointerException occurred at: %s.%s (Line: %d), Error Message: %s",
                        apiPath, className, methodName, lineNumber, e.getMessage());
            }
        }

        // Return response with enhanced error message
        return Result.fail(ExceptionUtil.getExceptionCode(e), enhancedMessage);
    }

    /**
     * Handle business exceptions
     *
     * @param request  Request object
     * @param response Response object
     * @param e        Business exception
     * @return Result Unified response result
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<String> handleBusinessException(HttpServletRequest request, HttpServletResponse response, BusinessException e) {
        // Get trace ID from MDC
        String traceId = MDC.get("traceId");
        String traceInfo = StringUtil.isNotBlank(traceId) ? " [TraceId: " + traceId + "]" : "";

        // Build comprehensive log message - 使用单行格式避免ES采集分割
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("<================================  业务异常处理   ================================>").append(traceInfo);
        logMessage.append(" | API Path: ").append(request.getRequestURI());
        logMessage.append(" | Request Method: ").append(request.getMethod());
        logMessage.append(" | Request Parameters: ").append(JsonUtil.toJson(request.getParameterMap()));

        // Get request body content
        String requestBody = getRequestBody(request);
        if (StringUtil.isNotBlank(requestBody)) {
            // 清理换行符，避免多行日志
            String cleanRequestBody = requestBody.replace("\n", "").replace("\r", "");
            logMessage.append(" | Request Body: ").append(cleanRequestBody);
        }

        logMessage.append(" | Business Exception Code: ").append(e.getCode());
        logMessage.append(" | Business Exception Message: ").append(e.getMessage() != null ? e.getMessage().replace("\n", "").replace("\r", "") : "null");
        logMessage.append(" | ").append("<================================  业务异常处理   ================================>").append(traceInfo);

        // Log as single warning message
        log.info(logMessage.toString());

        // Build error message including API path
        String enhancedMessage = String.format("API Path: %s, Error Message: %s", request.getRequestURI(), e.getMessage());

        // Return business exception response
        return Result.fail(e.getCode(), enhancedMessage);
    }

    /**
     * Handle parameter validation exceptions
     *
     * @param request  Request object
     * @param response Response object
     * @param e        Parameter validation exception
     * @return Result Unified response result
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<String> handleValidationException(HttpServletRequest request, HttpServletResponse response, MethodArgumentNotValidException e) {
        // Get trace ID from MDC
        String traceId = MDC.get("traceId");
        String traceInfo = StringUtil.isNotBlank(traceId) ? " [TraceId: " + traceId + "]" : "";

        // Get all validation errors
        String[] errors = e.getBindingResult().getAllErrors().stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .toArray(String[]::new);

        // Merge error messages
        String errorMessage = String.join(" | ", errors);

        // Build comprehensive log message - 使用单行格式避免ES采集分割
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("<================================  参数验证异常   ================================>").append(traceInfo);
        logMessage.append(" | API Path: ").append(request.getRequestURI());
        logMessage.append(" | Request Method: ").append(request.getMethod());
        logMessage.append(" | Request Parameters: ").append(JsonUtil.toJson(request.getParameterMap()));

        // Get request body content
        String requestBody = getRequestBody(request);
        if (StringUtil.isNotBlank(requestBody)) {
            // 清理换行符，避免多行日志
            String cleanRequestBody = requestBody.replace("\n", "").replace("\r", "");
            logMessage.append(" | Request Body: ").append(cleanRequestBody);
        }

        logMessage.append(" | Validation Errors: ").append(errorMessage);
        logMessage.append(" | ").append("<================================  参数验证异常   ================================>").append(traceInfo);

        // Log as single warning message
        log.info(logMessage.toString());

        // Build error message including API path
        String enhancedMessage = String.format("API Path: %s, Parameter Validation Error: %s", request.getRequestURI(), errorMessage);

        // Return parameter validation error response
        return Result.fail(HttpCode.PARAMETER_VERIFICATION_FAILED.getCode(), enhancedMessage);
    }

    /**
     * Get exception message
     *
     * @param e Exception
     * @return String Exception message
     */
    private String getErrorMessage(Exception e) {
        // Parameter validation exception
        if (e instanceof MethodArgumentNotValidException) {
            String[] errors = ((MethodArgumentNotValidException) e).getBindingResult().getAllErrors().stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .toArray(String[]::new);
            return String.join(" | ", errors);
        }
        // Excel exception
        if (e instanceof ExcelAnalysisException) {
            return ExceptionUtil.getSimpleMessage(e);
        }
        // Unchecked exception
        if (e instanceof UncheckedException) {
            return ExceptionUtil.getSimpleMessage(e);
        }
        // Other cases
        return StringUtil.emptyToDefault(ExceptionUtil.getSimpleMessage(e), "Internal Server Error");
    }

    /**
     * Get request body content from HttpServletRequest
     *
     * @param request HttpServletRequest
     * @return String Request body content
     */
    private String getRequestBody(HttpServletRequest request) {
        try {
            // Check if request has body content
            String contentType = request.getContentType();
            if (contentType == null || request.getContentLength() <= 0) {
                return null;
            }

            // Only read body for POST, PUT, PATCH methods with JSON or XML content
            String method = request.getMethod();
            if (!"POST".equalsIgnoreCase(method) && !"PUT".equalsIgnoreCase(method) && !"PATCH".equalsIgnoreCase(method)) {
                return null;
            }

            // Only read specific content types to avoid binary data
            if (!contentType.contains("application/json") &&
                    !contentType.contains("application/xml") &&
                    !contentType.contains("text/xml") &&
                    !contentType.contains("text/plain")) {
                return "Binary or unsupported content type: " + contentType;
            }

            // Try to read from request body
            StringBuilder body = new StringBuilder();
            try (BufferedReader reader = request.getReader()) {
                String line;
                while ((line = reader.readLine()) != null) {
                    body.append(line);
                }
            }

            String bodyContent = body.toString();
            // Limit body content length to avoid huge logs
            if (bodyContent.length() > 2000) {
                return bodyContent.substring(0, 2000) + "... (truncated)";
            }

            return StringUtil.isBlank(bodyContent) ? null : bodyContent;
        } catch (IOException e) {
            // If we can't read the body, just log the error and continue
            log.error("Failed to read request body: {}", e.getMessage());
            return "Failed to read request body: " + e.getMessage();
        } catch (Exception e) {
            // Catch any other exceptions to prevent breaking the exception handling
            log.error("Unexpected error reading request body: {}", e.getMessage());
            return "Error reading request body: " + e.getMessage();
        }
    }

    /**
     * 构建简约的堆栈信息，便于快速定位问题
     * 只包含项目相关的堆栈信息，过滤掉框架和第三方库的堆栈
     *
     * @param ex 异常对象
     * @return 简约的堆栈信息字符串
     */
    private String buildCompactStackTrace(Exception ex) {
        if (ex == null || ex.getStackTrace() == null || ex.getStackTrace().length == 0) {
            return "";
        }

        StringBuilder compactStack = new StringBuilder();
        StackTraceElement[] stackTrace = ex.getStackTrace();
        int relevantCount = 0;

        // 只取前5个相关的堆栈信息，避免日志过长
        for (StackTraceElement element : stackTrace) {
            if (relevantCount >= 5) {
                break;
            }

            String className = element.getClassName();

            // 只记录项目相关的堆栈信息，过滤掉框架和第三方库
            if (isRelevantStackTrace(className)) {
                if (compactStack.length() > 0) {
                    compactStack.append(" -> ");
                }

                // 简化类名，只保留最后两级
                String simplifiedClassName = simplifyClassName(className);
                compactStack.append(simplifiedClassName)
                          .append(".")
                          .append(element.getMethodName())
                          .append(":")
                          .append(element.getLineNumber());

                relevantCount++;
            }
        }

        return compactStack.toString();
    }

    /**
     * 判断是否是相关的堆栈信息
     * 只保留项目代码的堆栈，过滤掉框架和第三方库
     *
     * @param className 类名
     * @return 是否相关
     */
    private boolean isRelevantStackTrace(String className) {
        if (StringUtil.isBlank(className)) {
            return false;
        }

        // 包含项目包名的堆栈
        if (className.startsWith("cn.need.")) {
            return true;
        }

        // 过滤掉框架和第三方库的堆栈
        return !className.startsWith("org.springframework.") &&
               !className.startsWith("org.apache.") &&
               !className.startsWith("com.sun.") &&
               !className.startsWith("sun.") &&
               !className.startsWith("java.") &&
               !className.startsWith("javax.") &&
               !className.startsWith("org.eclipse.") &&
               !className.startsWith("org.junit.") &&
               !className.startsWith("com.intellij.") &&
               !className.startsWith("org.mybatis.") &&
               !className.startsWith("com.baomidou.") &&
               !className.startsWith("org.slf4j.") &&
               !className.startsWith("ch.qos.logback.");
    }

    /**
     * 简化类名，只保留最后两级包名
     *
     * @param fullClassName 完整类名
     * @return 简化后的类名
     */
    private String simplifyClassName(String fullClassName) {
        if (StringUtil.isBlank(fullClassName)) {
            return fullClassName;
        }

        String[] parts = fullClassName.split("\\.");
        if (parts.length <= 2) {
            return fullClassName;
        }

        // 保留最后两级：包名.类名
        return parts[parts.length - 2] + "." + parts[parts.length - 1];
    }
}