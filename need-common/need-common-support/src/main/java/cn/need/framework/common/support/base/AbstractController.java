package cn.need.framework.common.support.base;

import cn.need.framework.common.core.collection.ArrayUtil;
import cn.need.framework.common.core.exception.ExceptionUtil;
import cn.need.framework.common.core.exception.unchecked.UncheckedException;
import cn.need.framework.common.core.http.HttpCode;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.support.api.Result;
import com.alibaba.excel.exception.ExcelAnalysisException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ModelAttribute;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 控制器的超类
 * <p>
 * 1. 定义了统一的返回信息
 * <p>
 * 2. 定义了控制器的全局异常处理
 *
 * <AUTHOR>
 * @since 1.1.0
 */
public abstract class AbstractController {

    /**
     * 日志记录器
     */
    protected final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 全局request对象
     */
    protected HttpServletRequest request;

    /**
     * 全局response对象
     */
    protected HttpServletResponse response;


    /**
     * 为全局request、response对象赋值，每个请求调用之前都会执行该方法
     *
     * @param request  request对象
     * @param response response对象
     */
    @ModelAttribute
    public void setRequestAndResponse(HttpServletRequest request, HttpServletResponse response) {
        this.request = request;
        this.response = response;
    }

    /**
     * 响应成功
     *
     * @param <T> 泛型参数
     * @return Result<T> 统一响应结果
     */
    protected <T> Result<T> success() {
        return Result.ok();
    }

    /**
     * 响应成功，并设置响应数据
     *
     * @param data 响应数据
     * @param <T>  泛型参数
     * @return Result<T> 统一响应结果
     */
    protected <T> Result<T> success(T data) {
        return Result.ok(data);
    }

    /**
     * 响应成功，并设置响应数据、响应消息
     *
     * @param data    响应数据
     * @param message 响应消息
     * @param <T>     泛型参数
     * @return Result<T> 统一响应结果
     */
    protected <T> Result<T> success(T data, String message) {
        return Result.ok(data, message);
    }

    /**
     * 响应失败
     *
     * @param <T> 泛型参数
     * @return Result<T> 统一响应结果
     */
    protected <T> Result<T> failed() {
        return Result.fail();
    }

    /**
     * 响应失败，并设置响应消息
     *
     * @param message 响应消息
     * @param <T>     泛型参数
     * @return Result<T> 统一响应结果
     */
    protected <T> Result<T> failed(String message) {
        return Result.fail(message);
    }

    /**
     * 响应失败，并设置状态码、响应消息
     *
     * @param code    状态码
     * @param message 响应消息
     * @param <T>     泛型参数
     * @return Result<T> 统一响应结果
     */
    protected <T> Result<T> failed(int code, String message) {
        return Result.fail(code, message);
    }

    /**
     * 响应失败，并设置通用的HttpCode状态码常量
     *
     * @param httpCode 通用的HttpCode状态码常量
     * @param <T>      泛型参数
     * @return Result<T> 统一响应结果
     */
    protected <T> Result<T> failed(HttpCode httpCode) {
        return Result.fail(httpCode);
    }

    /**
     * 响应失败，并设置通用的HttpCode状态码常量、响应消息
     *
     * @param httpCode 通用的HttpCode状态码常量
     * @param message  响应消息
     * @param <T>      泛型参数
     * @return Result<T> 统一响应结果
     */
    protected <T> Result<T> failed(HttpCode httpCode, String message) {
        return Result.fail(httpCode, message);
    }

    /**
     * 响应失败，并设置通用的HttpCode状态码常量、响应消息
     *
     * @param e   异常信息
     * @param <T> 泛型参数
     * @return Result<T> 统一响应结果
     */
    protected <T> Result<T> failed(Exception e) {
        return Result.fail(e);
    }

    /**
     * 统一异常处理
     *
     * @param request  请求对象
     * @param response 响应对象
     * @param e        异常信息
     * @return Object 响应信息
     */
    @ExceptionHandler({Exception.class})
    protected Object exceptionHandler(HttpServletRequest request, HttpServletResponse response, Exception e) {
        // Get trace ID from MDC
        String traceId = MDC.get("traceId");
        String traceInfo = StringUtil.isNotBlank(traceId) ? " [TraceId: " + traceId + "]" : "";

        // 构建异常响应日志信息 - 使用单行格式避免ES采集分割
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("<================================  AbstractController异常处理   ================================>").append(traceInfo);
        logMessage.append(" | Status: ").append(response.getStatus());
        logMessage.append(" | Method: ").append(request.getMethod());
        logMessage.append(" | URI: ").append(request.getRequestURI());

        // 添加查询参数
        if (StringUtil.isNotBlank(request.getQueryString())) {
            logMessage.append("?").append(request.getQueryString());
        }

        // 添加请求参数
        if (!request.getParameterMap().isEmpty()) {
            logMessage.append(" | Request Parameters: ").append(JsonUtil.toJson(request.getParameterMap()));
        }

        // 添加异常信息
        logMessage.append(" | Exception: ").append(e.getClass().getSimpleName());
        logMessage.append(" | Exception Message: ").append(e.getMessage() != null ? e.getMessage().replace("\n", "").replace("\r", "") : "null");

        // 添加简约的堆栈信息
        String compactStackTrace = buildCompactStackTrace(e);
        if (StringUtil.isNotBlank(compactStackTrace)) {
            logMessage.append(" | Stack: ").append(compactStackTrace);
        }

        logMessage.append(" | ").append("<================================  AbstractController异常处理   ================================>").append(traceInfo);

        // 记录异常响应日志 - 使用单行日志，不再包含完整堆栈信息
        log.error(logMessage.toString());

        // 构建增强的错误消息，包含API路径信息
        String errorMessage = getErrorMessage(e);
        String enhancedMessage = String.format("API Path: %s, Error Message: %s", request.getRequestURI(), errorMessage);

        //直接获取异常信息返回
        return failed(ExceptionUtil.getExceptionCode(e), enhancedMessage);
    }

    /**
     * 获取异常消息
     *
     * @param e 异常
     * @return String 异常消息
     */
    private String getErrorMessage(Exception e) {
        //参数校验异常
        if (e instanceof MethodArgumentNotValidException) {
            String[] errors = ((MethodArgumentNotValidException) e).getBindingResult().getAllErrors().stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .toArray(String[]::new);
            return ArrayUtil.join(errors, " | ");
        }
        //excel异常
        if (e instanceof ExcelAnalysisException) {
            return ExceptionUtil.getSimpleMessage(e);
        }
        //Unchecked异常
        if (e instanceof UncheckedException) {
            return ExceptionUtil.getSimpleMessage(e);
        }
        //其他情况
        return StringUtil.emptyToDefault(ExceptionUtil.getSimpleMessage(e), "服务器内部异常");
    }
}
