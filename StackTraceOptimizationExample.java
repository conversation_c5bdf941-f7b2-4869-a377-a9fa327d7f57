/**
 * 堆栈信息优化示例
 * 展示优化前后的堆栈信息记录对比
 */
public class StackTraceOptimizationExample {
    
    public static void main(String[] args) {
        System.out.println("=== 堆栈信息优化示例 ===\n");
        
        // 模拟一个复杂的调用链异常
        try {
            simulateComplexException();
        } catch (Exception e) {
            System.out.println("【优化前】完整堆栈信息（冗长且难以快速定位）：");
            e.printStackTrace();
            
            System.out.println("\n" + "=".repeat(80) + "\n");
            
            System.out.println("【优化后】简约堆栈信息（简洁且便于排查）：");
            String compactStack = buildCompactStackTrace(e);
            System.out.println("Exception: " + e.getClass().getSimpleName());
            System.out.println("Message: " + e.getMessage());
            System.out.println("Stack: " + compactStack);
        }
    }
    
    private static void simulateComplexException() {
        // 模拟业务层调用
        businessMethod();
    }
    
    private static void businessMethod() {
        // 模拟服务层调用
        serviceMethod();
    }
    
    private static void serviceMethod() {
        // 模拟数据访问层调用
        daoMethod();
    }
    
    private static void daoMethod() {
        // 模拟一个空指针异常
        String data = null;
        data.length(); // 这里会抛出 NullPointerException
    }
    
    /**
     * 构建简约的堆栈信息
     */
    private static String buildCompactStackTrace(Exception ex) {
        if (ex == null || ex.getStackTrace() == null || ex.getStackTrace().length == 0) {
            return "";
        }
        
        StringBuilder compactStack = new StringBuilder();
        StackTraceElement[] stackTrace = ex.getStackTrace();
        int relevantCount = 0;
        
        // 只取前5个相关的堆栈信息
        for (StackTraceElement element : stackTrace) {
            if (relevantCount >= 5) {
                break;
            }
            
            String className = element.getClassName();
            
            // 只记录项目相关的堆栈信息
            if (isRelevantStackTrace(className)) {
                if (compactStack.length() > 0) {
                    compactStack.append(" -> ");
                }
                
                String simplifiedClassName = simplifyClassName(className);
                compactStack.append(simplifiedClassName)
                          .append(".")
                          .append(element.getMethodName())
                          .append(":")
                          .append(element.getLineNumber());
                
                relevantCount++;
            }
        }
        
        return compactStack.toString();
    }
    
    private static boolean isRelevantStackTrace(String className) {
        if (className == null || className.trim().isEmpty()) {
            return false;
        }
        
        // 包含项目包名的堆栈（这里用示例类名）
        if (className.contains("StackTraceOptimizationExample") || 
            className.startsWith("cn.need.")) {
            return true;
        }
        
        // 过滤掉框架和第三方库的堆栈
        return !className.startsWith("org.springframework.") &&
               !className.startsWith("org.apache.") &&
               !className.startsWith("com.sun.") &&
               !className.startsWith("sun.") &&
               !className.startsWith("java.") &&
               !className.startsWith("javax.") &&
               !className.startsWith("org.eclipse.") &&
               !className.startsWith("org.junit.") &&
               !className.startsWith("com.intellij.") &&
               !className.startsWith("org.mybatis.") &&
               !className.startsWith("com.baomidou.") &&
               !className.startsWith("org.slf4j.") &&
               !className.startsWith("ch.qos.logback.");
    }
    
    private static String simplifyClassName(String fullClassName) {
        if (fullClassName == null || fullClassName.trim().isEmpty()) {
            return fullClassName;
        }
        
        String[] parts = fullClassName.split("\\.");
        if (parts.length <= 2) {
            return fullClassName;
        }
        
        // 保留最后两级：包名.类名
        return parts[parts.length - 2] + "." + parts[parts.length - 1];
    }
}

/*
预期输出示例：

=== 堆栈信息优化示例 ===

【优化前】完整堆栈信息（冗长且难以快速定位）：
java.lang.NullPointerException
	at StackTraceOptimizationExample.daoMethod(StackTraceOptimizationExample.java:45)
	at StackTraceOptimizationExample.serviceMethod(StackTraceOptimizationExample.java:37)
	at StackTraceOptimizationExample.businessMethod(StackTraceOptimizationExample.java:32)
	at StackTraceOptimizationExample.simulateComplexException(StackTraceOptimizationExample.java:27)
	at StackTraceOptimizationExample.main(StackTraceOptimizationExample.java:11)

================================================================================

【优化后】简约堆栈信息（简洁且便于排查）：
Exception: NullPointerException
Message: null
Stack: StackTraceOptimizationExample.daoMethod:45 -> StackTraceOptimizationExample.serviceMethod:37 -> StackTraceOptimizationExample.businessMethod:32 -> StackTraceOptimizationExample.simulateComplexException:27 -> StackTraceOptimizationExample.main:11

优化效果：
1. 去除了框架和JDK的堆栈信息，只保留业务相关的调用链
2. 使用箭头（->）连接调用链，更直观地显示调用关系
3. 简化类名，只保留关键信息
4. 单行显示，便于ES日志收集和搜索
5. 限制堆栈深度，避免日志过长
*/
