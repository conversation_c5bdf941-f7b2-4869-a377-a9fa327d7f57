# 字段名修改总结

## 修改概述

根据用户需求，将查询参数中的时间字段从 `createTime` 修改为 `earliestTime`，并将 SQL 查询条件从 `<=` 修改为 `>=`，以正确表达查询语义。

## 修改详情

### 1. 字段语义变更

**修改前**:
- 字段名: `createTime`
- 语义: 创建时间，查询此时间点及之前的数据
- SQL 条件: `l.create_time <= #{qo.createTime}`

**修改后**:
- 字段名: `earliestTime`
- 语义: 最早时间，查询此时间点及之后的数据
- SQL 条件: `l.create_time >= #{qo.earliestTime}`

### 2. 修改的文件列表

#### Java 文件
1. **BinLocationLogChainQuery.java**
   - 字段名: `createTime` → `earliestTime`
   - 注释: 更新字段描述和 Schema 注解

2. **InventoryController.java**
   - 更新 API 注释中的描述

#### XML 文件
3. **BinLocationLogMapper.xml**
   - SQL 条件: `#{qo.createTime}` → `#{qo.earliestTime}`
   - 比较操作符: `<=` → `>=`
   - 两处修改：source 侧和 dest 侧的查询条件

#### 文档文件
4. **BinLocationLogChain_API_Documentation.md**
   - 更新请求参数示例
   - 更新参数说明表格
   - 更新 curl 示例
   - 更新注意事项

5. **SQL_Test_Example.sql**
   - 更新示例 SQL 中的条件
   - 更新注释说明

### 3. 查询逻辑变更

#### 修改前的查询逻辑
```sql
-- 查询指定时间点及之前的数据
WHERE l.create_time <= '2024-12-08 10:30:00'
```

#### 修改后的查询逻辑
```sql
-- 查询指定时间点及之后的数据
WHERE l.create_time >= '2024-12-08 10:30:00'
```

### 4. API 使用示例

#### 修改前
```json
{
  "createTime": "2024-12-08T10:30:00",
  "productId": 12345,
  "currentInStockQty": 100
}
```

#### 修改后
```json
{
  "earliestTime": "2024-12-08T10:30:00",
  "productId": 12345,
  "currentInStockQty": 100
}
```

### 5. 业务场景说明

这个修改更符合实际的业务场景：

1. **时间范围查询**: 用户指定一个最早时间点，查询从该时间点开始的所有库位变更记录
2. **库存追踪**: 从指定时间点开始追踪库存变化，而不是查询历史某个时间点之前的数据
3. **数据分析**: 便于分析某个时间段内的库存变化趋势

### 6. 向后兼容性

⚠️ **注意**: 这是一个破坏性变更，会影响：
- 前端调用需要更新字段名
- 现有的 API 调用需要修改参数名
- 查询结果的时间范围会发生变化

### 7. 测试建议

建议进行以下测试：
1. **单元测试**: 验证新的查询参数能正确传递到 SQL
2. **集成测试**: 验证 API 接口能正确处理新的参数名
3. **业务测试**: 验证查询结果符合预期的时间范围
4. **性能测试**: 确认 `>=` 条件的查询性能

### 8. 部署注意事项

1. **数据库索引**: 确认 `create_time` 字段有适当的索引支持 `>=` 查询
2. **前端更新**: 需要同步更新前端代码中的字段名
3. **文档更新**: 更新相关的 API 文档和开发文档
4. **版本管理**: 考虑 API 版本控制策略

## 总结

这次修改使字段名和查询逻辑更加语义化和直观，`earliestTime` + `>=` 的组合清楚地表达了"查询从指定时间点开始的数据"这一业务需求。
