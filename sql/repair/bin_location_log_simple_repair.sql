-- =====================================================
-- BinLocationLog简化修复SQL - 实际可用版本
-- 直接可执行的修复脚本
-- =====================================================

-- =====================================================
-- 1. 检测断链记录 - 简化版本
-- =====================================================

-- 检测所有断链记录
SELECT 
    broken_chains.*,
    CONCAT('产品版本:', broken_chains.product_version_id, 
           ', 库位:', broken_chains.bin_location_detail_id,
           ', 记录:', broken_chains.log_id,
           ', 侧面:', broken_chains.qty_side,
           ', 期望:', broken_chains.prev_after_qty,
           ', 实际:', broken_chains.before_qty) AS error_desc
FROM (
    SELECT
        qc.product_version_id,
        qc.bin_location_detail_id,
        qc.log_id,
        qc.qty_side,
        qc.before_qty,
        qc.after_qty,
        qc.change_qty,
        qc.prev_after_qty,
        qc.change_type,
        qc.create_time
    FROM (
        SELECT
            q.*,
            LAG(q.after_qty) OVER (
                PARTITION BY q.product_version_id, q.bin_location_detail_id
                ORDER BY q.create_time, q.log_id, q.qty_side
            ) AS prev_after_qty
        FROM (
            -- source 侧
            SELECT
                l.id AS log_id,
                l.product_version_id,
                l.source_bin_location_detail_id AS bin_location_detail_id,
                l.create_time,
                'source' AS qty_side,
                l.source_before_in_stock_qty AS before_qty,
                l.source_change_in_stock_qty AS change_qty,
                l.source_after_in_stock_qty AS after_qty,
                l.change_type
            FROM bin_location_log l
            WHERE l.remove_flag = 0
            
            UNION ALL
            
            -- dest 侧
            SELECT
                l.id,
                l.product_version_id,
                l.dest_bin_location_detail_id,
                l.create_time,
                'dest',
                l.dest_before_in_stock_qty,
                l.dest_change_in_stock_qty,
                l.dest_after_in_stock_qty,
                l.change_type
            FROM bin_location_log l
            WHERE NOT (
                l.dest_bin_location_detail_id = l.source_bin_location_detail_id
                AND l.dest_change_in_stock_qty = l.source_change_in_stock_qty
            )
            AND l.remove_flag = 0
        ) q
    ) qc
    WHERE qc.prev_after_qty IS NOT NULL
      AND qc.prev_after_qty <> qc.before_qty
) broken_chains
ORDER BY 
    broken_chains.product_version_id,
    broken_chains.bin_location_detail_id,
    broken_chains.create_time,
    broken_chains.log_id;

-- =====================================================
-- 2. 修复特定产品和库位的断链
-- 使用方法：替换下面的产品版本ID和库位详情ID
-- =====================================================

-- 设置要修复的产品版本ID和库位详情ID
SET @target_product_version_id = 12345;  -- 替换为实际值
SET @target_bin_location_detail_id = 67890;  -- 替换为实际值

-- 2.1 查看该产品库位的所有记录（按时间顺序）
SELECT
    qc.*,
    LAG(qc.after_qty) OVER (
        ORDER BY qc.create_time, qc.log_id, qc.qty_side
    ) AS prev_after_qty,
    ROW_NUMBER() OVER (
        ORDER BY qc.create_time, qc.log_id, qc.qty_side
    ) AS chain_order,
    CASE 
        WHEN LAG(qc.after_qty) OVER (ORDER BY qc.create_time, qc.log_id, qc.qty_side) = qc.before_qty 
        THEN 'OK' 
        ELSE 'BROKEN' 
    END AS chain_status
FROM (
    -- source 侧
    SELECT
        l.id AS log_id,
        l.product_version_id,
        l.source_bin_location_detail_id AS bin_location_detail_id,
        l.create_time,
        'source' AS qty_side,
        l.source_before_in_stock_qty AS before_qty,
        l.source_change_in_stock_qty AS change_qty,
        l.source_after_in_stock_qty AS after_qty,
        l.change_type
    FROM bin_location_log l
    WHERE l.remove_flag = 0
      AND l.product_version_id = @target_product_version_id
      AND l.source_bin_location_detail_id = @target_bin_location_detail_id
    
    UNION ALL
    
    -- dest 侧
    SELECT
        l.id,
        l.product_version_id,
        l.dest_bin_location_detail_id,
        l.create_time,
        'dest',
        l.dest_before_in_stock_qty,
        l.dest_change_in_stock_qty,
        l.dest_after_in_stock_qty,
        l.change_type
    FROM bin_location_log l
    WHERE NOT (
        l.dest_bin_location_detail_id = l.source_bin_location_detail_id
        AND l.dest_change_in_stock_qty = l.source_change_in_stock_qty
    )
    AND l.remove_flag = 0
    AND l.product_version_id = @target_product_version_id
    AND l.dest_bin_location_detail_id = @target_bin_location_detail_id
) qc
ORDER BY qc.create_time, qc.log_id, qc.qty_side;

-- =====================================================
-- 3. 实际修复SQL模板
-- 使用方法：根据上面查询的结果，手动填写具体的修复数值
-- =====================================================

-- 3.1 修复Source侧记录示例
-- 根据实际情况替换log_id和数值
UPDATE bin_location_log
SET
    source_before_in_stock_qty = CASE id
        WHEN 1001 THEN 100  -- 第一个断链记录，使用prev_after_qty
        WHEN 1002 THEN 90   -- 100 + (-10) = 90
        WHEN 1003 THEN 80   -- 90 + (-10) = 80
        -- 继续添加需要修复的记录...
        END,
    source_after_in_stock_qty = CASE id
        WHEN 1001 THEN 90   -- 100 + (-10) = 90
        WHEN 1002 THEN 80   -- 90 + (-10) = 80
        WHEN 1003 THEN 70   -- 80 + (-10) = 70
        -- 继续添加需要修复的记录...
        END
WHERE id IN (1001, 1002, 1003)  -- 替换为实际需要修复的log_id
  AND product_version_id = @target_product_version_id;

-- 3.2 修复Dest侧记录示例
UPDATE bin_location_log
SET
    dest_before_in_stock_qty = CASE id
        WHEN 1004 THEN 70   -- 使用正确的before_qty
        WHEN 1005 THEN 80   -- 70 + 10 = 80
        -- 继续添加需要修复的记录...
        END,
    dest_after_in_stock_qty = CASE id
        WHEN 1004 THEN 80   -- 70 + 10 = 80
        WHEN 1005 THEN 90   -- 80 + 10 = 90
        -- 继续添加需要修复的记录...
        END
WHERE id IN (1004, 1005)  -- 替换为实际需要修复的log_id
  AND product_version_id = @target_product_version_id;

-- 3.3 InventoryAudit记录的特殊修复
-- InventoryAudit操作：before_qty = after_qty, change_qty = 0
UPDATE bin_location_log
SET
    source_before_in_stock_qty = source_after_in_stock_qty,  -- before = after
    source_change_in_stock_qty = 0  -- change = 0
WHERE id = 1006  -- 替换为实际的InventoryAudit记录ID
  AND change_type = 'InventoryAudit'
  AND product_version_id = @target_product_version_id;

-- =====================================================
-- 4. 修复后验证
-- =====================================================

-- 4.1 验证修复结果 - 检查是否还有断链
SELECT 
    '修复后验证' AS check_type,
    COUNT(*) AS broken_count
FROM (
    SELECT
        qc.log_id,
        qc.qty_side,
        qc.before_qty,
        qc.after_qty,
        LAG(qc.after_qty) OVER (
            ORDER BY qc.create_time, qc.log_id, qc.qty_side
        ) AS prev_after_qty
    FROM (
        SELECT
            l.id AS log_id,
            l.create_time,
            'source' AS qty_side,
            l.source_before_in_stock_qty AS before_qty,
            l.source_after_in_stock_qty AS after_qty
        FROM bin_location_log l
        WHERE l.remove_flag = 0
          AND l.product_version_id = @target_product_version_id
          AND l.source_bin_location_detail_id = @target_bin_location_detail_id
        
        UNION ALL
        
        SELECT
            l.id,
            l.create_time,
            'dest',
            l.dest_before_in_stock_qty,
            l.dest_after_in_stock_qty
        FROM bin_location_log l
        WHERE NOT (
            l.dest_bin_location_detail_id = l.source_bin_location_detail_id
            AND l.dest_change_in_stock_qty = l.source_change_in_stock_qty
        )
        AND l.remove_flag = 0
        AND l.product_version_id = @target_product_version_id
        AND l.dest_bin_location_detail_id = @target_bin_location_detail_id
    ) qc
    ORDER BY qc.create_time, qc.log_id, qc.qty_side
) verified
WHERE verified.prev_after_qty IS NOT NULL
  AND verified.prev_after_qty <> verified.before_qty;

-- 4.2 显示修复后的完整链路
SELECT
    qc.*,
    LAG(qc.after_qty) OVER (
        ORDER BY qc.create_time, qc.log_id, qc.qty_side
    ) AS prev_after_qty,
    CASE 
        WHEN LAG(qc.after_qty) OVER (ORDER BY qc.create_time, qc.log_id, qc.qty_side) = qc.before_qty 
        THEN 'OK' 
        ELSE 'BROKEN' 
    END AS chain_status
FROM (
    SELECT
        l.id AS log_id,
        l.create_time,
        'source' AS qty_side,
        l.source_before_in_stock_qty AS before_qty,
        l.source_change_in_stock_qty AS change_qty,
        l.source_after_in_stock_qty AS after_qty,
        l.change_type
    FROM bin_location_log l
    WHERE l.remove_flag = 0
      AND l.product_version_id = @target_product_version_id
      AND l.source_bin_location_detail_id = @target_bin_location_detail_id
    
    UNION ALL
    
    SELECT
        l.id,
        l.create_time,
        'dest',
        l.dest_before_in_stock_qty,
        l.dest_change_in_stock_qty,
        l.dest_after_in_stock_qty,
        l.change_type
    FROM bin_location_log l
    WHERE NOT (
        l.dest_bin_location_detail_id = l.source_bin_location_detail_id
        AND l.dest_change_in_stock_qty = l.source_change_in_stock_qty
    )
    AND l.remove_flag = 0
    AND l.product_version_id = @target_product_version_id
    AND l.dest_bin_location_detail_id = @target_bin_location_detail_id
) qc
ORDER BY qc.create_time, qc.log_id, qc.qty_side;

-- =====================================================
-- 使用说明
-- =====================================================

/*
使用步骤：

1. 执行第1部分SQL检测所有断链记录
2. 选择要修复的产品版本ID和库位详情ID，设置变量
3. 执行第2部分SQL查看该产品库位的完整链路
4. 根据查询结果，手动计算正确的数值
5. 修改第3部分的UPDATE语句，填入正确的数值
6. 执行UPDATE语句进行修复
7. 执行第4部分SQL验证修复结果

注意事项：
- 修复前建议备份数据
- 在事务中执行，出错可回滚
- 修复计算：new_after_qty = new_before_qty + change_qty
- InventoryAudit特殊处理：before_qty = after_qty, change_qty = 0
*/
