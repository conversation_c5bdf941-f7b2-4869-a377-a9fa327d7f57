# BinLocationLog 链路查询 API 文档

## 概述

新增了一个用于查询 BinLocationLog 链路信息的 API，可以根据创建时间和产品ID查询库位日志链路信息，并支持计算当前总库存数量。

## API 接口

### 请求信息

- **URL**: `/api/biz/inventory/bin-location-log-chain`
- **方法**: `POST`
- **Content-Type**: `application/json`

### 请求参数

```json
{
  "earliestTime": "2024-12-08T10:30:00",
  "productId": 12345,
  "currentInStockQty": 100
}
```

#### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| earliestTime | LocalDateTime | 是 | 最早时间，查询此时间点及之后的数据 |
| productId | Long | 是 | 产品ID |
| currentInStockQty | Integer | 否 | 当前库存数量，用于计算 currentTotalInStockQty |

### 响应结果

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "productVersionId": 12345,
      "binLocationDetailId": 67890,
      "logId": 111,
      "qtySide": "source",
      "beforeQty": 100,
      "changeQty": -10,
      "afterQty": 90,
      "createTime": "2024-12-08T10:30:00",
      "prevAfterQty": 100,
      "isChainOk": 1,
      "changeType": "pick",
      "productRefNum": "SKU-XIONGGU-00004",
      "supplierSku": "SUPPLIER-SKU-001",
      "locationName": "A-01-01",
      "currentTotalInStockQty": 100,
      "binLocationId": 123,
      "productId": 12345,
      "binlocationId": 456
    }
  ]
}
```

#### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| productVersionId | Long | 产品版本ID |
| binLocationDetailId | Long | 库位详情ID |
| logId | Long | 日志ID |
| qtySide | String | 数量侧（source/dest） |
| beforeQty | Integer | 变更前数量 |
| changeQty | Integer | 变更数量 |
| afterQty | Integer | 变更后数量 |
| createTime | LocalDateTime | 创建时间 |
| prevAfterQty | Integer | 上一条记录的变更后数量 |
| isChainOk | Integer | 链路是否正常（1-正常，0-断档，null-首条记录） |
| changeType | String | 变更类型 |
| productRefNum | String | 产品编号（从缓存获取） |
| supplierSku | String | 供应商SKU（从缓存获取） |
| locationName | String | 库位名称（从缓存获取） |
| currentTotalInStockQty | Integer | 当前总库存数量（计算值） |
| binLocationId | Long | 库位ID |
| productId | Long | 产品ID |
| binlocationId | Long | 库位ID（bin_location表的ID） |

## 功能特性

### 1. 链路校验
- 使用窗口函数检查 BinLocationLog 记录的连续性
- `isChainOk` 字段标识链路是否正常：
  - `1`: 链路正常
  - `0`: 链路断档
  - `null`: 首条记录

### 2. 缓存数据填充
- 自动从缓存中获取产品信息（产品编号、供应商SKU）
- 自动从缓存中获取库位信息（库位名称）

### 3. 总库存计算（SQL 实现）
- 如果传递了 `currentInStockQty`，会在 SQL 中计算每一行的 `currentTotalInStockQty`
- 使用窗口函数 `SUM() OVER()` 进行累计计算
- 计算逻辑：`currentInStockQty - SUM(changeQty) + 当前行changeQty`
- 性能更优，减少 Java 代码复杂度

### 4. 数据去重
- 自动排除 source 和 dest 完全重复的记录
- 避免同一操作产生的重复日志

## 使用示例

### 查询特定产品的链路信息

```bash
curl -X POST "http://localhost:8080/api/biz/inventory/bin-location-log-chain" \
  -H "Content-Type: application/json" \
  -d '{
    "earliestTime": "2024-12-08T10:30:00",
    "productId": 12345,
    "currentInStockQty": 100
  }'
```

### 不计算总库存的查询

```bash
curl -X POST "http://localhost:8080/api/biz/inventory/bin-location-log-chain" \
  -H "Content-Type: application/json" \
  -d '{
    "earliestTime": "2024-12-08T10:30:00",
    "productId": 12345
  }'
```

## 注意事项

1. 查询结果按 `create_time DESC, log_id DESC` 排序
2. 只查询 `remove_flag = 0` 的有效记录
3. 查询 `earliestTime` 及之后的数据（使用 `>=` 条件）
4. 缓存数据获取失败不会影响基础数据的返回
5. 如果不传递 `currentInStockQty`，`currentTotalInStockQty` 字段将为 `null`
